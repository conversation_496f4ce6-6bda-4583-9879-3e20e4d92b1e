import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class KnowledgeBaseConfig:
    """知识库配置类"""
    chunk_size: int = 10
    output_dir: str = "data_kb"
    file_prefix: str = "part"
    encoding: str = "utf-8"

class KnowledgeBaseBuilder:
    """知识库构建器类"""

    def __init__(self, config: Optional[KnowledgeBaseConfig] = None):
        """
        初始化知识库构建器

        Args:
            config: 知识库配置，如果为None则使用默认配置
        """
        self.config = config or KnowledgeBaseConfig()
        self.logger = logging.getLogger(self.__class__.__name__)

    def extractContexts(self, dataset: List[Dict[str, Any]]) -> List[str]:
        """
        从数据集中提取context字段并去重

        Args:
            dataset: 包含context字段的数据集

        Returns:
            去重后的context列表

        Raises:
            ValueError: 当数据集为空或格式不正确时
        """
        if not dataset:
            raise ValueError("数据集不能为空")

        contexts = []
        for idx, item in enumerate(dataset):
            if not isinstance(item, dict):
                self.logger.warning(f"跳过第{idx}条数据：不是字典格式")
                continue

            if 'context' not in item:
                self.logger.warning(f"跳过第{idx}条数据：缺少context字段")
                continue

            context = item['context']
            if context and isinstance(context, str):
                contexts.append(context.strip())
            else:
                self.logger.warning(f"跳过第{idx}条数据：context字段为空或非字符串")

        # 去重并保持顺序
        unique_contexts = list(dict.fromkeys(contexts))
        self.logger.info(f"从{len(dataset)}条数据中提取到{len(contexts)}个context，去重后得到{len(unique_contexts)}个语料")

        return unique_contexts

    def createOutputDirectory(self) -> Path:
        """
        创建输出目录

        Returns:
            输出目录的Path对象
        """
        output_path = Path(self.config.output_dir)
        try:
            output_path.mkdir(exist_ok=True)
            self.logger.info(f"创建输出目录：{output_path.absolute()}")
            return output_path
        except Exception as e:
            self.logger.error(f"创建输出目录失败：{e}")
            raise

    def writeChunksToFiles(self, contexts: List[str], output_dir: Path) -> List[Path]:
        """
        将context列表按chunk_size分组写入文件

        Args:
            contexts: context列表
            output_dir: 输出目录

        Returns:
            创建的文件路径列表
        """
        if not contexts:
            self.logger.warning("没有context数据需要写入")
            return []

        chunk_size = self.config.chunk_size
        total_files = (len(contexts) + chunk_size - 1) // chunk_size
        created_files = []

        self.logger.info(f"开始写入{len(contexts)}条数据到{total_files}个文件中")

        for i in range(total_files):
            try:
                # 获取当前chunk的数据
                start_idx = i * chunk_size
                end_idx = min((i + 1) * chunk_size, len(contexts))
                chunk = contexts[start_idx:end_idx]

                # 生成文件名
                file_name = f"{self.config.file_prefix}_{i+1}.txt"
                file_path = output_dir / file_name

                # 写入文件
                with open(file_path, "w", encoding=self.config.encoding) as f:
                    f.write("\n".join(chunk))

                created_files.append(file_path)
                self.logger.info(f"文件 {file_path} 写入完成，包含{len(chunk)}条数据")

            except Exception as e:
                self.logger.error(f"写入文件 {file_name} 时发生错误：{e}")
                raise

        return created_files

    def createKnowledgeBase(self, dataset: List[Dict[str, Any]]) -> List[Path]:
        """
        基于数据集中的context字段创建知识库

        Args:
            dataset: 包含context字段的数据集

        Returns:
            创建的文件路径列表

        Raises:
            ValueError: 当数据集格式不正确时
            IOError: 当文件操作失败时
        """
        try:
            self.logger.info("开始创建知识库...")

            # 提取并去重context
            contexts = self.extractContexts(dataset)

            if not contexts:
                self.logger.warning("没有有效的context数据，跳过文件创建")
                return []

            # 创建输出目录
            output_dir = self.createOutputDirectory()

            # 写入文件
            created_files = self.writeChunksToFiles(contexts, output_dir)

            self.logger.info(f"知识库创建完成，共创建{len(created_files)}个文件")
            return created_files

        except Exception as e:
            self.logger.error(f"创建知识库时发生错误：{e}")
            raise

def displayFileContent(file_path: str, max_lines: int = 20) -> None:
    """
    显示文件内容

    Args:
        file_path: 文件路径
        max_lines: 最大显示行数
    """
    try:
        file_path_obj = Path(file_path)
        if not file_path_obj.exists():
            logger.warning(f"文件不存在：{file_path}")
            return

        with open(file_path_obj, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        logger.info(f"文件 {file_path} 内容预览（前{min(max_lines, len(lines))}行）：")
        for i, line in enumerate(lines[:max_lines], 1):
            print(f"{i:3d}: {line.rstrip()}")

        if len(lines) > max_lines:
            print(f"... 还有{len(lines) - max_lines}行内容")

    except Exception as e:
        logger.error(f"读取文件 {file_path} 时发生错误：{e}")

# 主函数示例
def main():
    """主函数示例"""
    try:
        # 这里需要先加载数据集
        # from datasets import load_dataset
        # dataset = load_dataset('cmrc2018')

        # 创建知识库配置
        config = KnowledgeBaseConfig(
            chunk_size=10,
            output_dir="data_kb",
            file_prefix="part"
        )

        # 创建知识库构建器
        kb_builder = KnowledgeBaseBuilder(config)

        # 创建知识库（这里需要传入实际的数据集）
        # created_files = kb_builder.createKnowledgeBase(dataset['test'])

        # 显示第一个文件的内容
        # if created_files:
        #     displayFileContent(str(created_files[0]))

        logger.info(f"知识库构建器已创建，配置：{config}")
        logger.info("请先加载数据集后调用 kb_builder.createKnowledgeBase(dataset['test'])")

        return kb_builder

    except Exception as e:
        logger.error(f"主函数执行失败：{e}")

if __name__ == "__main__":
    main()