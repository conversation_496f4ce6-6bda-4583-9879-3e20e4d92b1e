# 导入LazyLLM主模块
import lazyllm

# 定义停止条件函数
# 当输入值大于10时停止循环
stop_func = lambda x: x > 10

# 定义循环体函数1：简单的乘法操作
module_func = lambda x: x * 2

# loop构建方式1：基本循环
# 循环执行module_func直到满足stop_condition
loop1 = lazyllm.loop(
    module_func,                # 循环体函数：每次将输入乘以2
    stop_condition=stop_func)   # 停止条件：当结果>10时停止

# 演示loop1的执行过程
# 输入1 -> 2 -> 4 -> 8 -> 16（>10，停止）
print('=== 基本循环演示 ===')
print('输入1，循环过程: 1 -> 2 -> 4 -> 8 -> 16')
print('1输出：', loop1(1))

#==========================
# 定义循环体函数2：返回多个值的复杂函数
def module_func2(x):
    """
    复杂的循环体函数，返回两个值：
    - x+1: 递增值
    - x*2: 倍增值
    """
    print(f"\t当前循环值: {x}")
    # 使用package将多个返回值打包
    return lazyllm.package(x+1, x*2)

# loop构建方式2：处理多返回值的循环
# judge_on_full_input=False表示停止条件只判断第一个返回值
loop2 = lazyllm.loop(
    module_func2,                    # 循环体函数：返回(x+1, x*2)
    stop_condition=stop_func,        # 停止条件：当第一个返回值>10时停止
    judge_on_full_input=False)       # 只用第一个返回值判断停止条件

# 演示loop2的执行过程
# 输入1 -> (2,2) -> (3,4) -> (4,6) -> ... -> (11,20)（第一个值>10，停止）
print('\n=== 多返回值循环演示 ===')
print('输入1，每次返回(x+1, x*2)，当第一个值>10时停止')
print('2输出：', loop2(1))