#!/usr/bin/env python3
"""
测试智谱 GLM embedding-3 模型的实际输出维度
"""

from lazyllm import OnlineEmbeddingModule

def test_embedding_dimension():
    """测试 embedding-3 模型的实际输出维度"""
    
    # 创建 embedding 模型实例
    embedding_model = OnlineEmbeddingModule(
        source="glm",
        embed_model_name="embedding-3",
        api_key="e37e81fae0e04aab9340b25a2f351e42.c34b4fosyCYIJKhb",
        embed_url="https://open.bigmodel.cn/api/paas/v4/embeddings/",
    )
    
    # 测试文本
    test_text = "这是一个测试文本"
    
    try:
        # 获取向量
        vector = embedding_model(test_text)
        
        print(f"测试文本: {test_text}")
        print(f"向量类型: {type(vector)}")
        print(f"向量维度: {len(vector) if isinstance(vector, (list, tuple)) else 'unknown'}")
        
        # 显示向量的前几个元素
        if isinstance(vector, (list, tuple)) and len(vector) > 0:
            print(f"向量前5个元素: {vector[:5]}")
            
        return len(vector) if isinstance(vector, (list, tuple)) else None
        
    except Exception as e:
        print(f"测试失败: {e}")
        return None

if __name__ == "__main__":
    print("=== 测试智谱 GLM embedding-3 模型维度 ===")
    dimension = test_embedding_dimension()
    
    if dimension:
        print(f"\n结论: embedding-3 模型实际输出维度为 {dimension}")
        if dimension == 1024:
            print("✓ 维度符合预期 (1024)")
        elif dimension == 2048:
            print("⚠️  维度为 2048，与文档描述的 1024 不符")
        else:
            print(f"⚠️  维度为 {dimension}，需要进一步确认")
    else:
        print("❌ 无法确定向量维度")
