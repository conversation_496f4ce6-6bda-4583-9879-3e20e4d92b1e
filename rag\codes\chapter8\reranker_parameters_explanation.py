# 导入LazyLLM相关模块
import lazyllm
from lazyllm import pipeline, bind, OnlineEmbeddingModule, SentenceSplitter, Retriever, Reranker
from lazyllm.tools.rag import Document

# ===== 重排序器参数详解示例 =====
# 本文件演示Reranker组件的各种参数配置和输出格式

# 定义AI助手提示词
prompt = ('You will play the role of an AI Q&A assistant and complete a dialogue task. '
          'In this task, you need to provide your answer based on the given context and question.')

# ===== 文档和嵌入配置 =====

# 创建文档对象，配置嵌入模型
documents = Document(dataset_path="rag_master",
                     embed=OnlineEmbeddingModule(source="glm", embed_model_name="embedding-2"), manager=False)

# 创建句子级别的节点组
documents.create_node_group(name="sentences", transform=SentenceSplitter, chunk_size=1024, chunk_overlap=100)

# ===== 重排序器流水线配置 =====

with pipeline() as ppl:
    # 第一步：检索器配置
    # 使用余弦相似度进行初步检索，返回前3个结果
    ppl.retriever = Retriever(documents, group_name="sentences", similarity="cosine", similarity_cut_off=0.003, topk=3)

    # ===== 重排序器参数配置示例 =====

    # 1. 默认输出格式（DocNode对象列表）
    # ppl.reranker = Reranker(name="ModuleReranker",
    #                         model=lazyllm.OnlineEmbeddingModule(type="rerank", source="glm",
    #                                                             embed_model_name="rerank"),
    #                         topk=2) | bind(query=ppl.input)
    # 返回: [DocNode1, DocNode2]

    # 2. 字典格式输出
    # ppl.reranker = Reranker(name="ModuleReranker",
    #                         model=lazyllm.OnlineEmbeddingModule(type="rerank", source="glm",
    #                                                             embed_model_name="rerank"),
    #                         topk=2, output_format="dict") | bind(query=ppl.input)
    # 返回: [{"content": "文本1", "metadata": {...}}, {"content": "文本2", "metadata": {...}}]

    # 3. 字典格式输出 + 连接
    # ppl.reranker = Reranker(name="ModuleReranker",
    #                         model=lazyllm.OnlineEmbeddingModule(type="rerank", source="glm",
    #                                                             embed_model_name="rerank"),
    #                         topk=2, output_format="dict", join=True) | bind(query=ppl.input)
    # 返回: 合并后的字典结果

    # 4. 内容格式输出（仅文本内容）
    # ppl.reranker = Reranker(name="ModuleReranker",
    #                         model=lazyllm.OnlineEmbeddingModule(type="rerank", source="glm",
    #                                                             embed_model_name="rerank"),
    #                         topk=2, output_format="content" ) | bind(query=ppl.input)
    # 返回: ["文本1", "文本2"]

    # 5. 内容格式输出 + 默认连接
    # ppl.reranker = Reranker(name="ModuleReranker",
    #                         model=lazyllm.OnlineEmbeddingModule(type="rerank", source="glm",
    #                                                             embed_model_name="rerank"),
    #                         topk=2, output_format="content", join=True) | bind(query=ppl.input)
    # 返回: "文本1文本2"

    # 6. 内容格式输出 + 自定义分隔符连接（当前使用的配置）
    # name: "ModuleReranker" 指定重排序器类型
    # model: 使用GLM的在线重排序模型
    # topk: 重排序后返回前2个最相关结果
    # output_format: "content" 仅返回文本内容
    # join: 使用自定义分隔符连接多个结果
    # bind: 将查询绑定到流水线输入
    ppl.reranker = Reranker(name="ModuleReranker",
                            model=lazyllm.OnlineEmbeddingModule(type="rerank", source="glm",
                                                                embed_model_name="rerank"),
                            topk=2, output_format="content",
                            join='11111111111111111111111111111') | bind(query=ppl.input)

# ===== 执行重排序测试 =====

# 执行完整的检索+重排序流水线
query = "何为天道"
nodes = ppl(query)

# 输出结果
print("=== 重排序器参数配置演示 ===")
print(f"查询问题: {query}")
print(f"流程: 检索(topk=3) -> 重排序(topk=2) -> 自定义分隔符连接")
print(f"最终结果: {nodes}")
print("\n注意: 结果中的长串'1'就是我们设置的自定义分隔符")
