from openai import OpenAI



client = OpenAI(
    api_key='d210f6a1-7eff-4dd2-8778-ff3db4f8c54d',
    base_url='https://ark.cn-beijing.volces.com/api/v3',
)
model_type = 'ep-20250822223253-s98w6'
print(f'model_type: {model_type}')

messages = []
for query in ['你是谁？', "what's your name?", '你是谁研发的？']:
    messages.append({
        'role': 'user',
        'content': query
    })
    resp = client.chat.completions.create(
        model=model_type,
        messages=messages,
        seed=42)
    response = resp.choices[0].message.content
    print(f'query: {query}')
    print(f'response: {response}')
    messages.append({'role': 'assistant', 'content': response})

# 流式
for query in ['78654+657=?', '晚上睡不着觉怎么办']:
    messages.append({'role': 'user', 'content': query})
    stream_resp = client.chat.completions.create(
        model=model_type,
        messages=messages,
        stream=True,
        seed=42)
    print(f'query: {query}')
    print('response: ', end='')
    response = ''
    for chunk in stream_resp:
        response += chunk.choices[0].delta.content
        print(chunk.choices[0].delta.content, end='', flush=True)
    print()
    messages.append({'role': 'assistant', 'content': response})