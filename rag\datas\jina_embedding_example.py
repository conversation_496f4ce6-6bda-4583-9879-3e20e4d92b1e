#!/usr/bin/env python3
"""
Jina 嵌入模块使用示例
展示如何在 LazyLLM 知识库构建中使用自定义的 Jina 嵌入模块
"""

import logging
import os
from pathlib import Path
from datasets import load_dataset

# 导入自定义模块
from jina_embedding import createJinaEmbedding, OpenAICompatibleJinaEmbedding
from data_pre import KnowledgeBaseBuilder, KnowledgeBaseConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Jina API 配置
JINA_API_KEY = "jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR"

def testJinaEmbedding():
    """
    测试 Jina 嵌入模块的基本功能
    """
    logger.info("开始测试 Jina 嵌入模块...")
    
    try:
        # 创建 Jina 嵌入模块
        jina_embedding = createJinaEmbedding(
            api_key=JINA_API_KEY,
            model="jina-embeddings-v3",
            task="text-matching"
        )
        
        # 测试文本
        test_texts = [
            "人工智能是计算机科学的一个分支",
            "机器学习是人工智能的重要组成部分",
            "深度学习使用神经网络进行模式识别",
            "自然语言处理帮助计算机理解人类语言"
        ]
        
        logger.info(f"测试文本数量：{len(test_texts)}")
        
        # 生成嵌入向量
        embeddings = jina_embedding.embed(test_texts)
        
        logger.info(f"成功生成 {len(embeddings)} 个嵌入向量")
        logger.info(f"向量维度：{len(embeddings[0])}")
        
        # 测试单个文本
        single_embedding = jina_embedding.embed(test_texts[0])
        logger.info(f"单个文本嵌入向量维度：{len(single_embedding)}")
        
        return jina_embedding
        
    except Exception as e:
        logger.error(f"Jina 嵌入模块测试失败：{e}")
        raise

def testOpenAICompatibility():
    """
    测试 OpenAI 兼容性
    """
    logger.info("开始测试 OpenAI 兼容性...")
    
    try:
        # 创建 OpenAI 兼容的客户端
        openai_client = OpenAICompatibleJinaEmbedding(
            api_key=JINA_API_KEY,
            model="jina-embeddings-v3"
        )
        
        # 测试文本
        test_text = "这是一个测试OpenAI兼容性的文本"
        
        # 生成嵌入向量（OpenAI 格式）
        result = openai_client.create(input=test_text)
        
        logger.info(f"OpenAI 格式响应对象类型：{result['object']}")
        logger.info(f"模型名称：{result['model']}")
        logger.info(f"数据项数量：{len(result['data'])}")
        logger.info(f"向量维度：{len(result['data'][0]['embedding'])}")
        
        return openai_client
        
    except Exception as e:
        logger.error(f"OpenAI 兼容性测试失败：{e}")
        raise

def createKnowledgeBaseWithJinaEmbedding():
    """
    使用 Jina 嵌入创建知识库
    """
    logger.info("开始使用 Jina 嵌入创建知识库...")
    
    try:
        # 1. 加载数据集
        logger.info("加载 CMRC2018 数据集...")
        dataset = load_dataset('cmrc2018')
        
        # 2. 创建知识库配置
        kb_config = KnowledgeBaseConfig(
            chunk_size=5,  # 较小的分块，便于测试
            output_dir="jina_kb",
            file_prefix="jina_part",
            encoding="utf-8"
        )
        
        # 3. 创建知识库构建器
        kb_builder = KnowledgeBaseBuilder(kb_config)
        
        # 4. 创建知识库文件
        created_files = kb_builder.createKnowledgeBase(dataset['test'])
        
        if not created_files:
            logger.warning("没有创建任何知识库文件")
            return None, None
        
        logger.info(f"知识库文件创建完成，共 {len(created_files)} 个文件")
        
        # 5. 创建 Jina 嵌入模块
        jina_embedding = createJinaEmbedding(
            api_key=JINA_API_KEY,
            model="jina-embeddings-v3",
            task="text-matching",
            batch_size=10  # 小批量处理
        )
        
        # 6. 读取并嵌入知识库内容
        logger.info("开始为知识库内容生成嵌入向量...")
        
        all_texts = []
        file_text_mapping = {}
        
        for file_path in created_files[:3]:  # 只处理前3个文件作为示例
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                texts = [line.strip() for line in content.split('\n') if line.strip()]
                all_texts.extend(texts)
                file_text_mapping[str(file_path)] = texts
        
        logger.info(f"准备为 {len(all_texts)} 条文本生成嵌入向量...")
        
        # 生成嵌入向量
        embeddings = jina_embedding.embed(all_texts)
        
        logger.info(f"成功生成 {len(embeddings)} 个嵌入向量")
        logger.info(f"向量维度：{len(embeddings[0])}")
        
        # 7. 保存嵌入向量（示例）
        embedding_info = {
            'texts': all_texts,
            'embeddings': embeddings,
            'file_mapping': file_text_mapping,
            'vector_dim': len(embeddings[0]),
            'total_vectors': len(embeddings)
        }
        
        return created_files, embedding_info
        
    except Exception as e:
        logger.error(f"使用 Jina 嵌入创建知识库失败：{e}")
        raise

def demonstrateTextSimilarity(embedding_info):
    """
    演示文本相似度计算
    """
    if not embedding_info:
        logger.warning("没有嵌入信息，跳过相似度演示")
        return
    
    logger.info("开始演示文本相似度计算...")
    
    try:
        import numpy as np
        
        texts = embedding_info['texts']
        embeddings = np.array(embedding_info['embeddings'])
        
        # 选择第一个文本作为查询
        query_text = texts[0]
        query_embedding = embeddings[0]
        
        logger.info(f"查询文本：{query_text[:100]}...")
        
        # 计算余弦相似度
        similarities = []
        for i, embedding in enumerate(embeddings):
            # 余弦相似度计算
            dot_product = np.dot(query_embedding, embedding)
            norm_a = np.linalg.norm(query_embedding)
            norm_b = np.linalg.norm(embedding)
            similarity = dot_product / (norm_a * norm_b)
            similarities.append((i, similarity, texts[i]))
        
        # 排序并显示最相似的文本
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        logger.info("最相似的文本（前5个）：")
        for i, (idx, sim, text) in enumerate(similarities[:5]):
            logger.info(f"  {i+1}. 相似度: {sim:.4f} - {text[:80]}...")
        
    except ImportError:
        logger.warning("numpy 未安装，跳过相似度计算演示")
    except Exception as e:
        logger.error(f"相似度计算演示失败：{e}")

def main():
    """
    主函数：运行所有示例
    """
    logger.info("="*60)
    logger.info("Jina 嵌入模块完整示例")
    logger.info("="*60)
    
    try:
        # 1. 测试基本功能
        logger.info("\n1. 测试 Jina 嵌入模块基本功能")
        logger.info("-" * 40)
        jina_embedding = testJinaEmbedding()
        
        # 2. 测试 OpenAI 兼容性
        logger.info("\n2. 测试 OpenAI 兼容性")
        logger.info("-" * 40)
        openai_client = testOpenAICompatibility()
        
        # 3. 创建知识库并生成嵌入
        logger.info("\n3. 创建知识库并生成嵌入向量")
        logger.info("-" * 40)
        created_files, embedding_info = createKnowledgeBaseWithJinaEmbedding()
        
        # 4. 演示文本相似度
        logger.info("\n4. 演示文本相似度计算")
        logger.info("-" * 40)
        demonstrateTextSimilarity(embedding_info)
        
        logger.info("\n" + "="*60)
        logger.info("所有示例执行完成！")
        logger.info("="*60)
        
        # 输出总结信息
        if created_files and embedding_info:
            logger.info(f"\n总结信息：")
            logger.info(f"- 创建知识库文件：{len(created_files)} 个")
            logger.info(f"- 生成嵌入向量：{embedding_info['total_vectors']} 个")
            logger.info(f"- 向量维度：{embedding_info['vector_dim']}")
            logger.info(f"- 知识库目录：jina_kb/")
        
    except Exception as e:
        logger.error(f"示例执行失败：{e}")
        raise

if __name__ == "__main__":
    main()
