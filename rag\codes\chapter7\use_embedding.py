# ===== 环境配置说明 =====
# 使用本地模型需要设置模型路径环境变量：
# export LAZYLLM_MODEL_PATH=/path/to/your/models

# 使用在线模型需要设置API密钥环境变量，以商汤为例：
# export LAZYLLM_SENSENOVA_API_KEY=your_api_key
# export LAZYLLM_SENSENOVA_SECRET_KEY=your_secret_key

# 导入LazyLLM主模块和部署工具
import lazyllm
from lazyllm import deploy

# ===== 嵌入模型使用示例 =====

# 1. 在线嵌入模型：使用商汤的在线嵌入服务
# 优点：无需本地部署，调用简单
# 缺点：需要网络连接，可能有调用限制
online_embed = lazyllm.OnlineEmbeddingModule("sensenova")

# 2. 本地密集嵌入模型：BGE-Large中文版
# 优点：本地部署，无网络依赖，响应快
# 缺点：需要GPU资源，模型文件较大
offline_embed = lazyllm.TrainableModule('bge-large-zh-v1.5').start()

# 3. 本地稀疏嵌入模型：BGE-M3稀疏向量版本
# 特点：支持稀疏向量表示，适合某些特定场景
# embed_type: 'sparse' 指定使用稀疏嵌入模式
offline_sparse_embed = lazyllm.TrainableModule('bge-m3').deploy_method((deploy.AutoDeploy, {'embed_type': 'sparse'})).start()

# ===== 测试不同嵌入模型的效果 =====

# 测试文本
test_text = "hello world"

# 比较不同嵌入模型的输出
print("=== 嵌入模型对比测试 ===")
print("测试文本:", test_text)
print("\n在线嵌入结果: ", online_embed(test_text))
print("\n本地密集嵌入结果: ", offline_embed(test_text))
print("\n本地稀疏嵌入结果: ",  offline_sparse_embed(test_text))
