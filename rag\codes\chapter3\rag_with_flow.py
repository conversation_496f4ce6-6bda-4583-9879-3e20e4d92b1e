# 导入LazyLLM主模块
import lazyllm
# 导入bind函数用于数据绑定
from lazyllm import bind

# 定义AI问答助手的提示词
# 指导AI扮演问答助手角色，基于给定上下文和问题提供答案
prompt = ('You will act as an AI question-answering assistant and complete a dialogue task.'
          'In this task, you need to provide your answers based on the given context and questions.')

# 创建文档对象，指定CMRC2018数据集路径
documents = lazyllm.Document(dataset_path="/mnt/lustre/share_data/dist/cmrc2018/data_kb")

# 使用pipeline构建RAG流水线
with lazyllm.pipeline() as ppl:
    # 检索器：从文档中检索相关内容
    # group_name: 使用粗粒度文档块
    # similarity: 使用BM25中文相似度算法
    # topk: 返回前3个最相关的文档
    # output_format: 输出格式为内容
    # join: 使用空字符串连接多个结果
    ppl.retriever = lazyllm.Retriever(doc=documents, group_name="CoarseChunk", similarity="bm25_chinese",
                              topk=3, output_format='content', join='')

    # 格式化器：将检索结果和查询组合成字典格式
    # bind: 将查询绑定到pipeline的输入
    ppl.formatter = (lambda nodes, query: dict(context_str=nodes, query=query)) | bind(query=ppl.input)

    # 大语言模型：使用商汤在线聊天模块生成最终答案
    # ChatPrompter: 配置聊天提示器，指定额外的上下文键
    ppl.llm = lazyllm.OnlineChatModule(source="sensenova").prompt(lazyllm.ChatPrompter(instruction=prompt, extra_keys=['context_str']))

# 交互式问答
query = input('请输入您的问题\n')  # 获取用户输入的问题
res = ppl(query)  # 执行RAG流水线
print(f'With RAG Answer: {res}')  # 输出基于RAG的回答