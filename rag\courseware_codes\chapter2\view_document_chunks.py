#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门用于查看文档分割情况的脚本
"""

import sys
import os

# 直接导入lazyllm（在mcp环境中已安装）
import lazyllm

def view_document_chunks():
    """查看文档分割情况的主函数"""
    
    print("=== LazyLLM 文档分割查看器 ===\n")
    
    # 创建文档对象
    print("1. 创建文档对象...")
    documents = lazyllm.Document(dataset_path="datas")
    print(f"   文档对象类型: {type(documents)}")
    print(f"   文档对象: {documents}")
    
    # 查看可用的方法和属性
    print("\n2. 查看文档对象的可用方法...")
    available_methods = [method for method in dir(documents) if not method.startswith('_')]
    chunk_methods = [method for method in available_methods if 'chunk' in method.lower() or method in ['CoarseChunk', 'FineChunk', 'MediumChunk', 'Struct', 'ImgDesc']]
    print(f"   所有可用方法: {available_methods}")
    print(f"   分块相关方法: {chunk_methods}")
    
    # 方法1: 通过检索器查看分段
    print("\n3. 通过检索器查看文档分段...")
    try:
        # 创建检索器
        retriever = lazyllm.Retriever(
            doc=documents,
            group_name="CoarseChunk",
            similarity="bm25_chinese",
            topk=50  # 设置较大的topk来获取更多分段
        )
        
        # 使用通用查询获取分段
        query_terms = ["内容", "文档", "报告", "分析", "市场", "品牌"]
        all_chunks = set()
        
        for term in query_terms:
            try:
                chunks = retriever(query=term)
                for chunk in chunks:
                    # 使用内容的hash作为唯一标识
                    chunk_id = hash(chunk.get_content())
                    all_chunks.add((chunk_id, chunk))
            except Exception as e:
                print(f"   查询 '{term}' 时出错: {e}")
        
        # 转换为列表并按内容长度排序
        unique_chunks = [chunk for _, chunk in all_chunks]
        unique_chunks.sort(key=lambda x: len(x.get_content()), reverse=True)
        
        print(f"   通过检索器获取到 {len(unique_chunks)} 个唯一分段")
        
        # 显示分段统计信息
        if unique_chunks:
            lengths = [len(chunk.get_content()) for chunk in unique_chunks]
            print(f"   分段长度统计:")
            print(f"     最长分段: {max(lengths)} 字符")
            print(f"     最短分段: {min(lengths)} 字符")
            print(f"     平均长度: {sum(lengths) // len(lengths)} 字符")
            
            # 显示前几个分段的详细信息
            print(f"\n   前5个分段的详细信息:")
            for i, chunk in enumerate(unique_chunks[:5]):
                print(f"\n   --- 分段 {i+1} ---")
                print(f"   长度: {len(chunk.get_content())} 字符")
                print(f"   内容预览: {chunk.get_content()[:200]}...")
                if hasattr(chunk, 'metadata') and chunk.metadata:
                    print(f"   元数据: {chunk.metadata}")
                print("   " + "-" * 50)
        
    except Exception as e:
        print(f"   通过检索器查看分段时出错: {e}")
    
    # 方法2: 尝试直接访问分组方法
    print("\n4. 尝试直接访问分组方法...")
    group_methods = ['CoarseChunk', 'FineChunk', 'MediumChunk', 'Struct', 'ImgDesc']
    
    for group_name in group_methods:
        try:
            if hasattr(documents, group_name):
                print(f"\n   尝试访问 {group_name} 分组:")
                group_attr = getattr(documents, group_name)
                print(f"     属性类型: {type(group_attr)}")
                
                if callable(group_attr):
                    try:
                        nodes = group_attr()
                        if nodes:
                            print(f"     {group_name} 包含 {len(nodes)} 个节点")
                            # 显示前3个节点
                            for i, node in enumerate(nodes[:3]):
                                print(f"       节点 {i+1}: {len(node.get_content())} 字符")
                                print(f"         预览: {node.get_content()[:100]}...")
                        else:
                            print(f"     {group_name} 分组为空")
                    except Exception as e:
                        print(f"     调用 {group_name}() 时出错: {e}")
                else:
                    print(f"     {group_name} 不是可调用方法")
            else:
                print(f"   {group_name} 方法不存在")
        except Exception as e:
            print(f"   访问 {group_name} 时出错: {e}")
    
    # 方法3: 查看内部属性
    print("\n5. 查看文档对象的内部属性...")
    internal_attrs = ['_manager', '_nodes', 'node_groups', 'nodes', '_node_groups']
    
    for attr_name in internal_attrs:
        try:
            if hasattr(documents, attr_name):
                attr_value = getattr(documents, attr_name)
                print(f"   找到属性 {attr_name}: {type(attr_value)}")
                
                if attr_value is None:
                    print(f"     {attr_name} 为 None")
                elif isinstance(attr_value, dict):
                    print(f"     字典包含键: {list(attr_value.keys())}")
                    for key, value in attr_value.items():
                        if hasattr(value, '__len__'):
                            print(f"       {key}: {len(value)} 个元素")
                        else:
                            print(f"       {key}: {type(value)}")
                elif hasattr(attr_value, '__len__'):
                    print(f"     包含 {len(attr_value)} 个元素")
                elif hasattr(attr_value, '__dict__'):
                    print(f"     对象属性: {list(attr_value.__dict__.keys())}")
            else:
                print(f"   {attr_name} 属性不存在")
        except Exception as e:
            print(f"   访问 {attr_name} 时出错: {e}")
    
    print("\n=== 查看完成 ===")
    
    # 交互式查看特定分段
    while True:
        try:
            choice = input("\n是否要查看特定分段的完整内容？(输入分段编号，或 'q' 退出): ").strip()
            if choice.lower() == 'q':
                break
            
            chunk_num = int(choice) - 1
            if 0 <= chunk_num < len(unique_chunks):
                chunk = unique_chunks[chunk_num]
                print(f"\n=== 分段 {chunk_num + 1} 完整内容 ===")
                print(chunk.get_content())
                print("=" * 60)
            else:
                print(f"无效的分段编号，请输入 1-{len(unique_chunks)} 之间的数字")
        except ValueError:
            print("请输入有效的数字或 'q'")
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"查看分段时出错: {e}")

if __name__ == "__main__":
    view_document_chunks()
