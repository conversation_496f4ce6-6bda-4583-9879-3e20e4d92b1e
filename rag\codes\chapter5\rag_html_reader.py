# 导入操作系统相关模块
import os
# 导入LazyLLM主模块
import lazyllm
# 导入RAG文档处理模块
from lazyllm.tools.rag import Document
# 导入句子分割器
from lazyllm import SentenceSplitter
# 导入文档节点类
from lazyllm.tools.rag import DocNode
# 导入BeautifulSoup用于HTML解析
from bs4 import BeautifulSoup
# 导入流水线、并行处理、检索器、重排序器和绑定功能
from lazyllm import pipeline, parallel, Retriever, Reranker, bind


def processHtml(file, extra_info=None):
    """
    自定义HTML文件处理函数
    功能：将HTML文件转换为RAG系统可处理的文档节点格式
    """
    text = ''
    # 以UTF-8编码读取HTML文件
    with open(file, 'r', encoding='utf-8') as f:
        data = f.read()
        # 使用BeautifulSoup解析HTML内容
        soup = BeautifulSoup(data, 'lxml')
        # 提取所有文本内容，去除HTML标签
        for element in soup.stripped_strings:
            text += element + '\n'
    # 创建文档节点对象
    node = DocNode(text=text, metadata=extra_info or {})
    return [node]


# 创建文档对象，配置HTML文件处理
documents = Document(dataset_path=os.path.join(os.getcwd(), "rag_data"),
                     embed=lazyllm.OnlineEmbeddingModule(source="glm", embed_model_name="embedding-2"), manager=False)
# 为HTML文件注册自定义处理函数
documents.add_reader("*.html", processHtml)
# 创建句子级别的文档节点组
documents.create_node_group(name="sentences", transform=SentenceSplitter, chunk_size=1024, chunk_overlap=100)

# 定义AI问答助手的提示词
prompt = ('You will play the role of an AI Q&A assistant and complete a dialogue task. '
          'In this task, you need to provide your answer based on the given context and question.')

# 构建RAG流水线，支持HTML文件处理
with pipeline() as ppl:
    # 并行检索阶段
    with parallel().sum as ppl.prl:
        # 基于句子的余弦相似度检索
        ppl.prl.retriever1 = Retriever(documents, group_name="sentences", similarity="cosine", topk=3)
        # 基于粗粒度块的BM25中文检索
        ppl.prl.retriever2 = Retriever(documents, "CoarseChunk", "bm25_chinese", 0.003, topk=3)
    # 重排序阶段
    ppl.reranker = Reranker("ModuleReranker",
                            model=lazyllm.OnlineEmbeddingModule(type="rerank", source="glm",
                                                                embed_model_name="rerank"),
                            topk=1, output_format='content', join=True) | bind(query=ppl.input)
    # 格式化阶段
    ppl.formatter = (lambda nodes, query: dict(context_str=nodes, query=query)) | bind(query=ppl.input)
    # 大语言模型生成答案
    ppl.llm = lazyllm.OnlineChatModule(source='glm',
                                       model="glm-4",
                                       stream=False).prompt(lazyllm.ChatPrompter(prompt, extra_keys=["context_str"]))

# 测试HTML文档的RAG问答
print(ppl("全国住房城乡建设工作会议的主要内容"))
