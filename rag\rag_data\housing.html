<!DOCTYPE html>
    <html data-log-pv='{"mpc":39}'>
    <head>
        
    <title>全国住房城乡建设工作会议：大力推进商品住房销售制度改革 有力有序推行现房销售_发展</title>
<meta http-equiv="Cache-Control" content="no-transform" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<meta name="copyright" content="Copyright © 2017 Sohu.com Inc. All Rights Reserved." />

<meta name="mediaid" content="财联社"/>
<meta property="og:type" content="news"/>
<meta property="og:image" content=""/>
<meta property="og:url" content="www.sohu.com/a/*********_222256"/>
<meta property="og:release_date" content="2024-12-25 10:27"/>
<meta itemprop="dateUpdate" content="2024-12-25 10:27" />
<meta itemprop="datePublished" content="2024-12-25 10:27" />
<link rel="canonical" href="https://www.sohu.com/a/*********_222256"/>
<link rel="alternate" media="only screen and(max-width: 640px)" href="m.sohu.com/a/*********_222256"/>
<meta name="keywords" content="商品,现房,住房,制度,工作,改革,销售,会议,发展,大力,现房,住房,工作会议,房地产,财联社" />
<meta name="description" content="财联社12月25日电，全国住房城乡建设工作会议24日至25日在北京召开。会议指出，2025年，推动构建房地产发展新模式。一是着力优化和完善住房供应体系，加快发展保障性住房，满足城镇住房困难工薪群体刚性住房需求…" />
<meta property="og:description" content="财联社12月25日电，全国住房城乡建设工作会议24日至25日在北京召开。会议指出，2025年，推动构建房地产发展新模式。一是着力优化和完善住房供应体系，加快发展保障性住房，满足城镇住房困难工薪群体刚性住房需求…"/>
<meta property="og:title" content="全国住房城乡建设工作会议：大力推进商品住房销售制度改革 有力有序推行现房销售_发展"/>
        <meta charset="utf-8"/><meta name="data-spm" content="smpc"><meta name="renderer" content="webkit"><meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1"/><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1"/><link rel="dns-prefetch" href="//statics.itc.cn"><link rel="dns-prefetch" href="//g1.itc.cn"><link rel="dns-prefetch" href="//js.sohu.com"><link rel="icon" href="//statics.itc.cn/web/static/images/pic/sohu-logo/favicon.ico" type="image/x-icon"/><link rel="shortcut icon" href="//statics.itc.cn/web/static/images/pic/sohu-logo/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" sizes="57x57" href="//statics.itc.cn/web/static/images/pic/sohu-logo/logo-57.png"/><link rel="apple-touch-icon" sizes="72x72" href="//statics.itc.cn/web/static/images/pic/sohu-logo/logo-72.png"/><link rel="apple-touch-icon" sizes="114x114" href="//statics.itc.cn/web/static/images/pic/sohu-logo/logo-114.png"/><link rel="apple-touch-icon" sizes="144x144" href="//statics.itc.cn/web/static/images/pic/sohu-logo/logo-144.png"/><link rel="preload" href="https://g1.itc.cn/msfe-pcarti-prod/300000000000/assets/css/main_article-67aaa8.css" as="style"><link href="https://g1.itc.cn/msfe-pcarti-prod/300000000000/assets/css/main_article-67aaa8.css" rel="stylesheet">    </head>
            <body class="article-page" data-region="157" data-spm="content" data-newsid="*********">
        <div class="wrapper-box">
            <header id="main-header" class="error-head">
	<div class="head-container">
		<div class="head-nav" data-spm="nav">
			<ul>
			    <li class="index">
					<a data-clev="10220248" class="clearfix" target="_blank" href="http://www.sohu.com">
						<span class="sohu-logo"></span>
					</a>
				</li>
														<li class="nav-item"><a target="_blank" data-clev="10220249" href="http://news.sohu.com/">新闻</a></li>
																			<li class="nav-item"><a target="_blank" data-clev="10220250" href="http://sports.sohu.com/">体育</a></li>
																			<li class="nav-item"><a target="_blank" data-clev="10220251" href="http://auto.sohu.com/">汽车</a></li>
																			<li class="nav-item"><a target="_blank" data-clev="10220252" href="http://www.focus.cn/">房产</a></li>
																			<li class="nav-item"><a target="_blank" data-clev="10220253" href="http://travel.sohu.com/">旅游</a></li>
																			<li class="nav-item"><a target="_blank" data-clev="10220254" href="http://learning.sohu.com/">教育</a></li>
																			<li class="nav-item"><a target="_blank" data-clev="10220255" href="http://fashion.sohu.com/">时尚</a></li>
																			<li class="nav-item"><a target="_blank" data-clev="10220256" href="http://it.sohu.com/">科技</a></li>
																			<li class="nav-item"><a target="_blank" data-clev="10220257" href="http://business.sohu.com/">财经</a></li>
																			<li class="nav-item"><a target="_blank" data-clev="10220258" href="http://yule.sohu.com/">娱乐</a></li>
																																																																																																																									<li class="nav-item more-nav">
					<div class="more-tag" href="javascript:void(0)">
						<span>更多</span>
						<div class="cor"></div>
					</div>
					<more-nav id="moreNav"></more-nav>
				</li>
				
			</ul>
		</div>
		<head-right id="headRight"></head-right>
	</div>
</header>            <div class="location-without-nav"></div>
            <div class="area clearfix" id="article-container">
                <div class="column left" style="min-height: 200px">
	                    <div class="user-info" id="user-info" data-spm="author">
            <div class="user-pic">
            <!-- fromWhere为10是马甲号作者不可点击进入个人页面 -->
                            <a href="https://mp.sohu.com/profile?xpt=********************************" target="_blank">
                    <img data-src="//p8.itc.cn/q_70/images03/20231101/ec2e91a76895417992efaa425ecb7a69.jpeg" alt="">
                </a>
                    </div>
        <h4>
                    <a href="https://mp.sohu.com/profile?xpt=********************************" target="_blank">财联社</a>
                    </h4>
    <!-- 积分 -->
        <div style="height: 25px">
        <medal id="fox-integration"></medal>
    </div>
        <dl class="user-num">
        <dd><span class="value" data-value="492774" data-role="info-article-num"><em class="num"></em></span>文章</dd>
        <dd><span class="value" data-value="" data-role="info-read-num"><em class="num"></em></span>总阅读</dd>
    </dl>

    <!-- 企业认证 -->
            <ul class="company">
                    </ul>
    
    <!-- 非马甲号作者 -->
            <div class="user-more">
            <a href="https://mp.sohu.com/profile?xpt=********************************" target="_blank">查看TA的文章&gt;</a>
        </div>
    </div>        		<!-- 互动&分享 -->
	<div class="share-interaction" id="shareInteraction">
		<share-interaction/>
	</div>
</div>
                <div class="left main">
                    <div data-spm="content">
                        <div class="text">
                            
<div class="text-title">
    <h1>
                    全国住房城乡建设工作会议：大力推进商品住房销售制度改革 有力有序推行现房销售                <span class="article-tag">
                </span>
    </h1>
            <div class="article-info">
        <span class="time" id="news-time" data-val="1735093661000">
                        2024-12-25 10:27        </span>
                                <div class="area"><span>发布于：</span><span>上海市</span></div>
    </div>
</div>

<article class="article" id="mp-editor">
    <!-- 政务处理 -->
                                <p>【<span>全国住房城乡建设工作会议</span>：大力推进商品住房销售制度改革 有力有序推行现房销售】<span>财联社</span>12月25日电，全国住房城乡建设工作会议24日至25日在北京召开。会议指出，2025年，推动构建房地产发展新模式。一是着力优化和完善住房供应体系，加快发展<span>保障性住房</span>，满足城镇住房困难工薪群体刚性住房需求，支持城市政府<span>因城施策</span>，增加改善性住房特别是好房子供给。二是推动建立要素联动新机制，以编制实施住房发展规划和年度计划为抓手，以人定房、以房定地、以房定钱，促进房地产供需平衡、市场稳定。三是大力推进商品住房销售制度改革，有力有序推行现房销售，优化预售<span>资金监管</span>。四是加快建立房屋全生命周期安全管理制度，为房屋安全提供有力保障。五是完善房地产全过程监管，整治房地产市场秩序，切实维护群众合法权益。<a href="//www.sohu.com/?strategyid=00001 " target="_blank" title="点击进入搜狐首页" id="backsohucom" style="white-space: nowrap;"><span class="backword"><i class="backsohu"></i>返回搜狐，查看更多</span></a></p>                <!-- 政务账号添加来源标示处理 -->
        <!-- 政务账号添加来源标示处理 -->
        <p data-role="editor-name">责任编辑：<span></span></p>
    </article>
<div id="articleTransfer"><transfer/></div>
<!-- 评论禁言通知 -->
<div id="bannedNotice"><banned-notice/></div>
<div class="statement">平台声明：该文观点仅代表作者本人，搜狐号系信息发布平台，搜狐仅提供信息存储空间服务。</div><div class="bottom-relate-wrap clear type-3">
            <div id="article-like" data-like-type="type-3">
            <article-like/>
        </div>
        <div class="read-wrap">
        <span class="read-num">阅读 (<em data-role="pv" data-val="$articleStat.pv"></em>)</span>
    </div>
    <div id="articleReport">
        <report/>
    </div>
</div>

                            <div id="sohu-play-content"></div>
                        </div>
                    </div>
                    <div data-spm="middle-banner-ad">
                                            </div>
                                            <div id="articleAllsee" style='height:629px'><all-see-list/></div>
                                        <div class="_0u4o3bh76zbp"></div>
                    <div class="god-article-bottom" id="god_bottom_banner" data-spm="ad-text-bottom" style="display:block">
</div>
<div class="user-god clear" id="user-post" style="display:none">

</div>                    <!-- 评论 -->
                    <div id="meComment" style='min-height: 100px;'>
                        <me-comment/>
                    </div>
                    <div id="commentList">
                        <comment-list></comment-list>
                    </div>
                    <div id="discuss"></div>
                    <!-- 推荐阅读 -->
                                        <div style='min-height:1500px' id="groomRead">
                        <groom-read/>
                    </div>
                                    </div>
                <!-- 右侧边栏 -->
                <div class="sidebar right" id="right-side-bar" data-a="${isBaiDuAd}">
                    <right-side-bar/>
                </div>
             </div>
            <div id="float-btn"> <float-btn/> </div>
            <div class="left-bottom-float-fullScreenSleepContainer" style="display:none;">
    <div class="left-bottom-float-fullScreenSleep" style="display:none;" data-spm="ad-fullScreenSleep">
        <div class="close-tag"></div>
    </div>
</div>
<div class="left-bottom-float" id="left-bottom-god" data-spm="ad-ss">
</div>        </div>
        <script type="text/javascript">
                        window.deployEnv = "prod"
                    </script>
        <script src="//js.sohu.com/pv.js"></script><script src="https://g1.itc.cn/msfe-pcarti-prod/300000000000/assets/js/vendors-d407b7.js"></script><script src="https://g1.itc.cn/msfe-pcarti-prod/300000000000/assets/js/main_article-64e941.js"></script>        <script>
try {
        var cfgs = {
        channel_id: "39",
        news_id: "*********",
        cms_id: "$mpNews.cmsId",
        media_id: "222256",
        passport: "<EMAIL>",
        weboUrl: "https://mp.sohu.com/profile?xpt=********************************",
        title: "全国住房城乡建设工作会议：大力推进商品住房销售制度改革 有力有序推行现房销售",
        channel_url: "//house.sohu.com",
        integralLevel: "7",
        categoryId: "-1",
        //abData_fd用于abtest
        abData: "",
        // abData_discuss:"4", // 讨论
        abData_discuss: "", // 讨论
        abData_fd: "",
        abData_tw: "",
        originalId: "$mpNews.originalId",
        originalStatus: "10",
        isBaiDuAd: "",
        isPure: "${pure}",
        reprint: false,
        reprintSign: "",
        secureScore: '100',
        sGrade: '0',
        editor: '',
        hideAd: '',
        keywords: "[商品, 现房, 住房, 制度, 工作, 改革, 销售, 会议, 发展, 大力, 现房, 住房, 工作会议, 房地产, 财联社]",
        mpNewsExt: {
            "modelId": ""
        },
        imgsList: [
                                ],
        topNavigation: [
                                     {
                "url": "http://news.sohu.com/",
                "name": "新闻",
            }
                                    , {
                "url": "http://sports.sohu.com/",
                "name": "体育",
            }
                                    , {
                "url": "http://auto.sohu.com/",
                "name": "汽车",
            }
                                    , {
                "url": "http://www.focus.cn/",
                "name": "房产",
            }
                                    , {
                "url": "http://travel.sohu.com/",
                "name": "旅游",
            }
                                    , {
                "url": "http://learning.sohu.com/",
                "name": "教育",
            }
                                    , {
                "url": "http://fashion.sohu.com/",
                "name": "时尚",
            }
                                    , {
                "url": "http://it.sohu.com/",
                "name": "科技",
            }
                                    , {
                "url": "http://business.sohu.com/",
                "name": "财经",
            }
                                    , {
                "url": "http://yule.sohu.com/",
                "name": "娱乐",
            }
                                    , {
                "url": "http://baobao.sohu.com/",
                "name": "母婴",
            }
                                    , {
                "url": "https://healthnews.sohu.com/",
                "name": "健康",
            }
                                    , {
                "url": "http://history.sohu.com/",
                "name": "历史",
            }
                                    , {
                "url": "http://mil.sohu.com/",
                "name": "军事",
            }
                                    , {
                "url": "http://chihe.sohu.com/",
                "name": "美食",
            }
                                    , {
                "url": "http://cul.sohu.com/",
                "name": "文化",
            }
                                    , {
                "url": "http://astro.sohu.com/",
                "name": "星座",
            }
                                    , {
                "url": "https://www.sohu.com/xchannel/TURBd01EQXhPVGt5",
                "name": "专题",
            }
                                    , {
                "url": "http://game.sohu.com/",
                "name": "游戏",
            }
                                    , {
                "url": "http://fun.sohu.com/",
                "name": "搞笑",
            }
                                    , {
                "url": "http://acg.sohu.com/",
                "name": "动漫",
            }
                                    , {
                "url": "http://pets.sohu.com/",
                "name": "宠物",
            }
                                ],
        // 展示模式控制（0-无限制 1-8分 2-登录 3-订阅 4-付费）
        displayMode: "0",
        // 文章类型（客户端获取剩余内容接口路径参数）
        stage: "",
        // 前插视频字段
        videoId: "",
        site: "",
        HVTitle: ""
    }
} catch (e) {
    var html = '<div class="err-js">' +
        '<span><em class="icon err-js-icon"></em>JS加载错误，请重新加载。</span>' +
        '<a href="javascript:window.location.reload()" target="_blank" class="cached-btn"' +
        '><em class="icon-cached"></em>刷新</a></div>';
    $(document.body).prepend(html);
    console.error("发生错误", e);
}
</script>
<script>
try {
    const articleBlock = cfgs.displayMode == '1'
    if (articleBlock) {
        window.sohu_mp.contentControl(cfgs);
    } else {
        window.sohu_mp.article(cfgs);
    }
}
catch (e) {
    var html = '<div class="err-js">' +
        '<span><em class="icon err-js-icon"></em>JS加载错误，请重新加载。</span>' +
        '<a href="javascript:window.location.reload()" target="_blank" class="cached-btn"' +
        '><em class="icon-cached"></em>刷新</a></div>';
    $(document.body).prepend(html);
    console.error("发生错误", e);
}
</script>
<!-- 文章安全分低于等于10分不执行seo优化 -->
<script>
    (function(){
        var bp = document.createElement('script');
        var curProtocol = window.location.protocol.split(':')[0];
        if (curProtocol === 'https') {
            bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';        
        }
        else {
            bp.src = 'http://push.zhanzhang.baidu.com/push.js';
        }
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(bp, s);
    })();
</script>
<script type="text/javascript" src="https://cpro.baidustatic.com/cpro/ui/c.js" async="async" defer="defer"></script>        <!-- 头条SEO上报JS -->
        <script>
            (function(){
                var el = document.createElement("script");
                el.src = "https://lf1-cdn-tos.bytegoofy.com/goofy/ttzz/push.js?2a4809d3df819205088b399807ab2dfb6008be35d3aa4b8fc28d959eee7f7b82c112ff4abe50733e0ff1e1071a0fdc024b166ea2a296840a50a5288f35e2ca42";
                el.id = "ttzz";
                var s = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(el, s);
            })(window)
        </script>
    </body>
</html>