#!/usr/bin/env python3
"""
Jina AI 嵌入模块
提供符合 OpenAI 格式的 Jina AI 嵌入服务接口
"""

import json
import logging
import requests
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class JinaEmbeddingConfig:
    """Jina 嵌入配置类"""
    api_key: str
    model: str = "jina-embeddings-v3"
    task: str = "text-matching"
    base_url: str = "https://api.jina.ai/v1/embeddings"
    max_retries: int = 3
    timeout: int = 30
    batch_size: int = 100

class JinaEmbeddingModule:
    """
    Jina AI 嵌入模块
    提供符合 OpenAI 格式的嵌入服务
    """
    
    def __init__(self, config: JinaEmbeddingConfig):
        """
        初始化 Jina 嵌入模块
        
        Args:
            config: Jina 嵌入配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 设置请求头
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config.api_key}"
        }
        
        self.logger.info(f"Jina 嵌入模块初始化完成，模型：{self.config.model}")
    
    def _makeRequest(self, texts: List[str]) -> Dict[str, Any]:
        """
        发送嵌入请求到 Jina API
        
        Args:
            texts: 要嵌入的文本列表
            
        Returns:
            API 响应结果
            
        Raises:
            Exception: 当请求失败时
        """
        data = {
            "model": self.config.model,
            "task": self.config.task,
            "input": texts
        }
        
        for attempt in range(self.config.max_retries):
            try:
                self.logger.debug(f"发送嵌入请求，文本数量：{len(texts)}，尝试次数：{attempt + 1}")
                
                response = requests.post(
                    self.config.base_url,
                    headers=self.headers,
                    data=json.dumps(data),
                    timeout=self.config.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.logger.debug(f"嵌入请求成功，返回 {len(result.get('data', []))} 个向量")
                    return result
                else:
                    self.logger.warning(f"请求失败，状态码：{response.status_code}，响应：{response.text}")
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"请求超时，尝试次数：{attempt + 1}")
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"请求异常：{e}，尝试次数：{attempt + 1}")
            except Exception as e:
                self.logger.error(f"未知错误：{e}")
                
            # 重试前等待
            if attempt < self.config.max_retries - 1:
                wait_time = 2 ** attempt  # 指数退避
                self.logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        raise Exception(f"嵌入请求失败，已重试 {self.config.max_retries} 次")
    
    def _batchProcess(self, texts: List[str]) -> List[List[float]]:
        """
        批量处理文本嵌入
        
        Args:
            texts: 要嵌入的文本列表
            
        Returns:
            嵌入向量列表
        """
        all_embeddings = []
        batch_size = self.config.batch_size
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            self.logger.info(f"处理批次 {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")
            
            try:
                response = self._makeRequest(batch)
                
                # 提取嵌入向量
                embeddings = []
                for item in response.get('data', []):
                    embeddings.append(item.get('embedding', []))
                
                all_embeddings.extend(embeddings)
                
            except Exception as e:
                self.logger.error(f"批次处理失败：{e}")
                raise
        
        return all_embeddings
    
    def embed(self, texts: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
        """
        生成文本嵌入向量（符合 OpenAI 格式）
        
        Args:
            texts: 单个文本或文本列表
            
        Returns:
            单个嵌入向量或嵌入向量列表
        """
        # 处理输入格式
        if isinstance(texts, str):
            texts = [texts]
            return_single = True
        else:
            return_single = False
        
        # 验证输入
        if not texts:
            raise ValueError("输入文本不能为空")
        
        # 过滤空文本
        valid_texts = [text.strip() for text in texts if text and text.strip()]
        if not valid_texts:
            raise ValueError("没有有效的文本输入")
        
        self.logger.info(f"开始生成嵌入向量，文本数量：{len(valid_texts)}")
        
        try:
            # 批量处理
            embeddings = self._batchProcess(valid_texts)
            
            if len(embeddings) != len(valid_texts):
                raise Exception(f"嵌入向量数量不匹配：期望 {len(valid_texts)}，实际 {len(embeddings)}")
            
            self.logger.info(f"嵌入向量生成完成，向量维度：{len(embeddings[0]) if embeddings else 0}")
            
            # 返回格式处理
            if return_single:
                return embeddings[0] if embeddings else []
            else:
                return embeddings
                
        except Exception as e:
            self.logger.error(f"嵌入向量生成失败：{e}")
            raise
    
    def __call__(self, texts: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
        """
        使模块可调用，符合 LazyLLM 接口规范
        
        Args:
            texts: 单个文本或文本列表
            
        Returns:
            单个嵌入向量或嵌入向量列表
        """
        return self.embed(texts)

def createJinaEmbedding(api_key: str, 
                       model: str = "jina-embeddings-v3",
                       task: str = "text-matching",
                       **kwargs) -> JinaEmbeddingModule:
    """
    创建 Jina 嵌入模块的便捷函数
    
    Args:
        api_key: Jina API 密钥
        model: 模型名称
        task: 任务类型
        **kwargs: 其他配置参数
        
    Returns:
        JinaEmbeddingModule 实例
    """
    config = JinaEmbeddingConfig(
        api_key=api_key,
        model=model,
        task=task,
        **kwargs
    )
    
    return JinaEmbeddingModule(config)

# 兼容 OpenAI 格式的包装器
class OpenAICompatibleJinaEmbedding:
    """
    OpenAI 兼容的 Jina 嵌入包装器
    """
    
    def __init__(self, api_key: str, model: str = "jina-embeddings-v3"):
        self.jina_module = createJinaEmbedding(api_key, model)
    
    def create(self, input: Union[str, List[str]], model: str = None) -> Dict[str, Any]:
        """
        创建嵌入向量（OpenAI 格式）
        
        Args:
            input: 输入文本
            model: 模型名称（可选）
            
        Returns:
            OpenAI 格式的响应
        """
        embeddings = self.jina_module.embed(input)
        
        # 转换为 OpenAI 格式
        if isinstance(input, str):
            data = [{"object": "embedding", "embedding": embeddings, "index": 0}]
        else:
            data = [
                {"object": "embedding", "embedding": emb, "index": i}
                for i, emb in enumerate(embeddings)
            ]
        
        return {
            "object": "list",
            "data": data,
            "model": model or self.jina_module.config.model,
            "usage": {
                "prompt_tokens": len(input) if isinstance(input, str) else sum(len(text) for text in input),
                "total_tokens": len(input) if isinstance(input, str) else sum(len(text) for text in input)
            }
        }

if __name__ == "__main__":
    # 测试示例
    api_key = "jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR"
    
    # 创建嵌入模块
    jina_embedding = createJinaEmbedding(api_key)
    
    # 测试文本
    test_texts = [
        "这是一个测试文本",
        "This is a test text",
        "Ceci est un texte de test"
    ]
    
    try:
        # 生成嵌入向量
        embeddings = jina_embedding.embed(test_texts)
        print(f"生成了 {len(embeddings)} 个嵌入向量")
        print(f"向量维度：{len(embeddings[0])}")
        
        # 测试 OpenAI 兼容格式
        openai_client = OpenAICompatibleJinaEmbedding(api_key)
        result = openai_client.create(test_texts[0])
        print(f"OpenAI 格式测试成功：{result['object']}")
        
    except Exception as e:
        print(f"测试失败：{e}")
