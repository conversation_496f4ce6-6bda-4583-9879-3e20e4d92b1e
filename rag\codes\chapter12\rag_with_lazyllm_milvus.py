import lazyllm
from lazyllm import Reranker

# from online_models import custom_embedding_model as embedding_model, llm, rerank_model
from lazyllm import OnlineChatModule, OnlineEmbeddingModule
from lazyllm.module import OnlineEmbeddingModuleBase

llm = OnlineChatModule(
    source="openai",
    model="ep-20250822223253-s98w6",
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
    base_url="https://ark.cn-beijing.volces.com/api/v3/",  # 注意：末尾添加了斜杠
    stream=False,
)

embedding_model = OnlineEmbeddingModule(
    source="glm",
    embed_model_name="embedding-3",
    api_key="e37e81fae0e04aab9340b25a2f351e42.c34b4fosyCYIJKhb",
    embed_url="https://open.bigmodel.cn/api/paas/v4/embeddings/",  # 注意：末尾添加了斜杠
)

rerank_model = OnlineEmbeddingModule(
    source="qwen",
    api_key="sk-282f3112bd714d6e85540da173b5517c",
    type="rerank"
)


DOC_PATH = "rag_data" 

# store_conf = {
#     'type': 'map',
#     'indices': {
#         'smart_embedding_index': {
#             'backend': 'milvus',
#             'kwargs': {
#                 'uri': "http://192.168.31.237:19530",
#                 'index_kwargs': {
#                     'index_type': 'HNSW',
#                     'metric_type': 'COSINE',
#                     # 'params': {
#                     #     'M': 16,
#                     #     'efConstruction': 200
#                     # }
#                 }
#             },
#         },
#     },
# }
# milvus存储和索引配置
store_conf = {
    'type': 'milvus',
    'kwargs': {
        'uri': "http://192.168.31.237:19530",
        'index_kwargs': {
        'index_type': 'HNSW',
        'metric_type': 'COSINE',
        }
    }
}
docs = lazyllm.Document(dataset_path=DOC_PATH, embed=embedding_model, store_conf=store_conf)

# 使用smart_embedding_index索引，调用milvus向量检索
retriever = lazyllm.Retriever(docs, group_name="MediumChunk", topk=6, index='smart_embedding_index')
retriever.start()

reranker = Reranker('ModuleReranker', model=rerank_model, topk=3)

prompt = '你是一个友好的 AI 问答助手，你需要根据给定的上下文和问题提供答案。\
        根据以下资料回答问题：\
        {context_str} \n '
llm.prompt(lazyllm.ChatPrompter(instruction=prompt, extra_keys=['context_str']))

query = "证券管理有哪些准则？"

nodes = retriever(query=query)
rerank_nodes = reranker(nodes, query)

context_str = "\n======\n".join([node.get_content() for node in rerank_nodes])
res = llm({"query": query, "context_str": context_str})
print(res)
