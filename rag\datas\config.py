#!/usr/bin/env python3
"""
配置文件
管理 API 密钥和其他配置参数
"""

import os
from typing import Optional

class Config:
    """配置管理类"""
    
    # Jina AI 配置
    JINA_API_KEY: Optional[str] = os.getenv(
        'JINA_API_KEY', 
        'jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR'
    )
    
    JINA_MODEL: str = os.getenv('JINA_MODEL', 'jina-embeddings-v3')
    JINA_TASK: str = os.getenv('JINA_TASK', 'text-matching')
    JINA_BASE_URL: str = os.getenv('JINA_BASE_URL', 'https://api.jina.ai/v1/embeddings')
    
    # 知识库配置
    DEFAULT_CHUNK_SIZE: int = int(os.getenv('DEFAULT_CHUNK_SIZE', '10'))
    DEFAULT_OUTPUT_DIR: str = os.getenv('DEFAULT_OUTPUT_DIR', 'data_kb')
    DEFAULT_FILE_PREFIX: str = os.getenv('DEFAULT_FILE_PREFIX', 'part')
    
    # 日志配置
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    
    @classmethod
    def validate(cls) -> bool:
        """验证配置是否有效"""
        if not cls.JINA_API_KEY:
            raise ValueError("JINA_API_KEY 未设置")
        return True

# 全局配置实例
config = Config()

# 验证配置
try:
    config.validate()
except ValueError as e:
    print(f"配置验证失败：{e}")
    print("请设置环境变量 JINA_API_KEY 或在 config.py 中直接设置")
