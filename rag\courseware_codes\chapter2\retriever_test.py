# Part0

import lazyllm

# Part1

documents = lazyllm.Document(dataset_path="datas/中国茶饮市场Top5品牌分析报告.pdf",
                            #  embed=lazyllm.OnlineEmbeddingModule(),
                            #  manager=False
                             )
# data = documents._impl._reader.load_data(input_files=["datas/中国茶饮市场Top5品牌分析报告.pdf"])
# 查看文档分段情况
print("=== 文档分段情况查看 ===")

# 方法1: 直接打印documents对象信息
print(f"Documents对象: {documents}")
print(f"Documents类型: {type(documents)}")

# 方法2: 查看documents的属性和方法
print(f"Documents可用属性和方法:")
available_methods = [method for method in dir(documents) if not method.startswith('_')]
print(f"  可用方法: {available_methods}")

# 方法3: 尝试不同的获取节点方法
try:
    # 尝试使用node_groups属性
    if hasattr(documents, 'node_groups'):
        print(f"\nnode_groups: {documents.node_groups}")
        for group_name, nodes in documents.node_groups.items():
            print(f"  分组 '{group_name}': {len(nodes)} 个节点")
            # 查看前3个节点
            for i, node in enumerate(nodes[:3]):
                print(f"    节点 {i+1}: {type(node)} - 内容长度: {len(str(node))}")
                print(f"      内容预览: {str(node)[:100]}...")
except Exception as e:
    print(f"访问node_groups时出错: {e}")

try:
    # 尝试使用其他可能的属性
    if hasattr(documents, '_nodes'):
        print(f"\n_nodes属性存在，包含 {len(documents._nodes)} 个节点")
    elif hasattr(documents, 'nodes'):
        print(f"\nnodes属性存在，包含 {len(documents.nodes)} 个节点")
except Exception as e:
    print(f"访问节点属性时出错: {e}")

print("\n" + "="*50)

# Part2

# 添加一个简单的方法来查看文档分割情况
print("\n=== 简单查看文档分割情况 ===")
try:
    # 尝试通过检索器获取所有节点
    temp_retriever = lazyllm.Retriever(doc=documents,
                                      group_name="CoarseChunk",
                                      similarity="bm25_chinese",
                                      topk=100)  # 设置较大的topk来获取更多节点

    # 使用一个通用查询来获取节点
    all_nodes = temp_retriever(query="文档内容")
    print(f"通过检索器获取到 {len(all_nodes)} 个分段")

    # 显示前几个分段的信息
    for i, node in enumerate(all_nodes[:5]):
        print(f"\n分段 {i+1}:")
        print(f"  内容长度: {len(node.get_content())}")
        print(f"  内容预览: {node.get_content()[:150]}...")
        if hasattr(node, 'metadata'):
            print(f"  元数据: {node.metadata}")
        print("-" * 40)

except Exception as e:
    print(f"通过检索器查看分段时出错: {e}")

retriever = lazyllm.Retriever(doc=documents,
                              group_name="CoarseChunk",
                              similarity="bm25_chinese",
                              topk=3)

# Part3
llm = lazyllm.OnlineChatModule(
    source="openai",
    model="ep-20250822223253-s98w6",
    base_url="https://ark.cn-beijing.volces.com/api/v3/",
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
    stream=True,
    )   
# llm = lazyllm.OnlineChatModule()

# Part4

prompt = '你将扮演一个人工智能问答助手的角色，完成一项对话任务。在这个任务中，你需要根据给定的上下文以及问题，给出你的回答。'
llm.prompt(lazyllm.ChatPrompter(instruction=prompt, extra_keys=['context_str']))

# Part5

query = input("query(enter 'quit' to exit): ")
if query == "quit":
    exit(0)

# Part6

doc_node_list = retriever(query=query)

res = llm({
    "query": query,
    "context_str": "".join([node.get_content() for node in doc_node_list]),
})

# Part7

print(f"answer: {res}")

# 额外功能：查看检索到的分段详情
print("\n=== 检索到的分段详情 ===")
for i, node in enumerate(doc_node_list):
    print(f"\n检索分段 {i+1}:")
    print(f"  相似度分数: {getattr(node, 'score', 'N/A')}")
    print(f"  内容长度: {len(node.get_content())}")
    print(f"  完整内容: {node.get_content()}")
    if hasattr(node, 'metadata'):
        print(f"  元数据: {node.metadata}")
    print("-" * 30)

# 查看所有分组的详细信息
view_all = input("\n是否查看所有分组的详细信息？(y/n): ")
if view_all.lower() == 'y':
    try:
        print(f"\n=== 尝试多种方式查看文档分组 ===")

        # 方法1: 检查可用的分组方法
        available_groups = []
        for attr in ['CoarseChunk', 'FineChunk', 'MediumChunk', 'Struct', 'ImgDesc']:
            if hasattr(documents, attr):
                available_groups.append(attr)
        print(f"可用的分组方法: {available_groups}")

        # 方法2: 尝试直接调用分组方法
        for group_name in available_groups:
            try:
                print(f"\n--- 尝试获取 {group_name} 分组 ---")
                group_method = getattr(documents, group_name)
                if callable(group_method):
                    # 如果是方法，尝试调用
                    nodes = group_method()
                    if nodes:
                        print(f"{group_name} 分组包含 {len(nodes)} 个节点")
                        for i, node in enumerate(nodes[:3]):  # 只显示前3个
                            print(f"  节点 {i+1}:")
                            print(f"    内容长度: {len(node.get_content())}")
                            print(f"    内容预览: {node.get_content()[:200]}...")
                            if hasattr(node, 'metadata'):
                                print(f"    元数据: {node.metadata}")
                            print("-" * 30)
                    else:
                        print(f"{group_name} 分组为空")
                else:
                    print(f"{group_name} 不是可调用方法")
            except Exception as e:
                print(f"获取 {group_name} 分组时出错: {e}")

        # 方法3: 尝试通过内部属性访问
        print(f"\n--- 尝试通过内部属性访问 ---")
        for attr_name in ['_node_groups', '_nodes', 'node_groups', 'nodes']:
            if hasattr(documents, attr_name):
                try:
                    attr_value = getattr(documents, attr_name)
                    print(f"找到属性 {attr_name}: {type(attr_value)}")
                    if isinstance(attr_value, dict):
                        for key, value in attr_value.items():
                            print(f"  {key}: {len(value) if hasattr(value, '__len__') else value}")
                    elif hasattr(attr_value, '__len__'):
                        print(f"  包含 {len(attr_value)} 个元素")
                except Exception as e:
                    print(f"访问 {attr_name} 时出错: {e}")

        # 方法4: 查看文档管理器
        if hasattr(documents, '_manager') and documents._manager:
            try:
                print(f"\n--- 通过文档管理器查看 ---")
                manager = documents._manager
                print(f"文档管理器类型: {type(manager)}")
                if hasattr(manager, 'get_nodes'):
                    nodes = manager.get_nodes()
                    print(f"管理器中的节点数: {len(nodes)}")
                elif hasattr(manager, 'nodes'):
                    nodes = manager.nodes
                    print(f"管理器中的节点数: {len(nodes)}")
            except Exception as e:
                print(f"通过管理器访问时出错: {e}")

    except Exception as e:
        print(f"查看分组信息时出错: {e}")