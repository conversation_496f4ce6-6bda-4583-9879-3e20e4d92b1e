# 导入LazyLLM相关模块
from lazyllm import Document, Retriever, Reranker, OnlineEmbeddingModule, TrainableModule

# ===== 模型配置部分 =====

# 定义嵌入模型：BGE-Large中文版
# 用于将文本转换为向量表示，支持语义相似度计算
embedding_model = TrainableModule('bge-large-zh-v1.5').start()

# 重排序模型配置选项
# 在线重排序模型（需要API密钥）
# 目前LazyLLM仅支持 qwen和glm 在线重排模型，请指定相应的 API key
# online_rerank = OnlineEmbeddingModule(type="rerank")

# 本地重排序模型：BGE-Reranker-Large
# 用于对检索结果进行重新排序，提高检索精度
offline_rerank = TrainableModule('bge-reranker-large').start()

# ===== 文档处理配置 =====

# 创建文档对象，加载CMRC2018数据集
docs = Document("/mnt/lustre/share_data/dist/cmrc2018/data_kb", embed=embedding_model)

# 创建自定义节点组：按行分割文档
# 这种分割方式适合处理结构化的文本数据
docs.create_node_group(name='block', transform=(lambda d: d.split('\n')))

# ===== 检索器配置 =====

# 定义检索器：使用余弦相似度算法
# group_name: 使用自定义的"block"节点组
# similarity: 余弦相似度算法
# topk: 返回前3个最相关的文档
retriever = Retriever(docs, group_name="block", similarity="cosine", topk=3)

# ===== 重排序器配置 =====

# 重排序器配置选项说明：
# 1. 字典格式输出（包含完整信息）
# reranker1 = Reranker('ModuleReranker', model=online_rerank, topk=3, output_format='dict')
# 输出格式：包含 content, embedding 和 metadata 关键字的字典
# 若不指定output_format参数，则与retriever输出格式相同

# 2. 内容格式输出（仅返回文本内容）
# 'ModuleReranker': 指定重排序器类型
# model: 使用本地重排序模型
# topk: 最终返回前3个最相关的文档
# output_format: 'content' 表示只输出文本内容
# join: True 表示将多个结果串联成一个字符串，False时输出为字符串列表
reranker2 = Reranker('ModuleReranker', model=offline_rerank, topk=3, output_format='content', join=True)

# ===== 检索和重排序执行 =====

# 定义查询问题
query = "猴面包树有哪些功效？"

# 第一步：使用检索器进行初步检索
result1 = retriever(query=query)

# 第二步：使用重排序器对检索结果进行重新排序
# 重排序器会根据查询与文档的相关性重新排列结果
result2 = reranker2(result1, query=query)

# ===== 结果对比展示 =====

print("=== 检索与重排序效果对比 ===")
print(f"查询问题: {query}\n")

print("【初步检索结果】（余弦相似度）")
print("特点: 基于向量相似度的初步筛选")
print("-" * 50)
print("\n\n".join([res.get_content() for res in result1]))

print("\n" + "="*60 + "\n")

print("【重排序优化结果】（BGE-Reranker）")
print("特点: 基于深度学习模型的精确重排序")
print("-" * 50)
print(result2)
