# 导入LazyLLM主模块
import lazyllm

# 定义条件判断函数
is_positive = lambda x: x > 0  # 判断是否为正数
is_negative = lambda x: x < 0  # 判断是否为负数

# 定义每个条件对应的分支处理函数
positive_path = lambda x: 2 * x    # 正数路径：乘以2
negative_path = lambda x : -x      # 负数路径：取相反数
default_path = lambda x : '000'    # 默认路径：返回字符串'000'

# switch构建方式1：输入同时作为条件函数和分支函数的参数
# 这是最常见的使用方式，输入值既用于判断条件，也用于执行对应的分支函数
switch1 = lazyllm.switch(
    is_positive, positive_path,    # 如果is_positive(x)为True，执行positive_path(x)
    is_negative, negative_path,    # 如果is_negative(x)为True，执行negative_path(x)
    'default', default_path)       # 如果以上条件都不满足，执行default_path(x)

# 演示switch1的使用
print('\n=== 模式1：输入x同时作为条件函数和分支函数的输入 ===')
print("输入2（正数）: ", switch1(2))   # 2>0为True，执行positive_path(2) = 2*2 = 4
print("输入0（零）:   ", switch1(0))   # 0既不>0也不<0，执行default_path(0) = '000'
print("输入-5（负数）:", switch1(-5))  # -5<0为True，执行negative_path(-5) = -(-5) = 5

# switch构建方式2：条件函数和分支函数使用不同的输入
# 通过设置judge_on_full_input=False，可以分别为条件函数和分支函数提供不同的参数
switch2 = lazyllm.switch(
    is_positive, positive_path,
    is_negative, negative_path,
    'default', default_path,
    judge_on_full_input=False)     # 关键参数：允许条件函数和分支函数使用不同输入

# 演示switch2的使用
print('\n=== 模式2：条件函数和分支函数使用不同的输入 ===')
print("输入(-1,2): ", switch2(-1,2))  # -1传给条件函数，-1<0为True，2传给negative_path(2) = -2
print("输入(1,2):  ", switch2(1,2))   # 1传给条件函数，1>0为True，2传给positive_path(2) = 4
print("输入(0,2):  ", switch2(0, 2))  # 0传给条件函数，既不>0也不<0，2传给default_path(2) = '000'
