#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的RAG测试脚本
基于可以运行的prompt_with_llm_base.py配置
"""

import lazyllm

# 使用与prompt_with_llm_base.py相同的配置
print("=== 测试LLM模块 ===")
llm_default = lazyllm.OnlineChatModule(
    source="openai",
    model="ep-20250822223253-s98w6",
    base_url="https://ark.cn-beijing.volces.com/api/v3/",  # 注意末尾的斜杠
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
    stream=False,
)

# 测试LLM是否可以单独工作
try:
    test_result = llm_default("你好，请简单介绍一下自己")
    print(f"✓ LLM模块测试成功: {test_result}")
except Exception as e:
    print(f"✗ LLM模块测试失败: {e}")
    exit(1)

print("\n=== 测试嵌入模块 ===")
try:
    embedding_default = lazyllm.OnlineEmbeddingModule(
        source="glm",
        embed_model_name="embedding-2",
        api_key="e37e81fae0e04aab9340b25a2f351e42.c34b4fosyCYIJKhb",
    )
    
    test_embedding = embedding_default("测试文本")
    print(f"✓ 嵌入模块测试成功，向量维度: {len(test_embedding) if isinstance(test_embedding, list) else 'unknown'}")
except Exception as e:
    print(f"✗ 嵌入模块测试失败: {e}")
    print("继续测试，但可能影响RAG功能")

print("\n=== 测试简单的提示词功能 ===")
try:
    # 测试带提示词的LLM
    prompt = (
        "You will play the role of an AI Q&A assistant and complete a dialogue task. "
        "In this task, you need to provide your answer based on the given context and question."
    )
    
    llm_with_prompt = llm_default.prompt(
        lazyllm.ChatPrompter(prompt, extra_keys=["context_str"])
    )
    
    # 测试格式化输入
    test_input = {
        "context_str": "茶饮料市场在中国发展迅速，包括奶茶、果茶等多种类型。",
        "query": "茶饮料的市场情况如何？"
    }
    
    result = llm_with_prompt(test_input)
    print(f"✓ 提示词功能测试成功: {result}")
    
except Exception as e:
    print(f"✗ 提示词功能测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 测试完成 ===")
print("如果上述测试都成功，说明基础配置正确，可以继续测试完整的RAG流水线")
