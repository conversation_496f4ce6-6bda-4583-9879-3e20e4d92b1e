# ===== 相似度算法对比实验 =====
# 探讨不同相似度算法对检索召回效果的影响
# 环境配置：请将在线嵌入模型API密钥设置为环境变量或使用本地模型

# 导入LazyLLM相关模块
from lazyllm import Document, Retriever, TrainableModule

# ===== 嵌入模型配置 =====
# 定义本地嵌入模型：BGE-Large中文版
# 该模型专门针对中文文本优化，提供高质量的向量表示
embed_model = TrainableModule("bge-large-zh-v1.5").start()

# ===== 文档处理配置 =====
# 加载CMRC2018中文阅读理解数据集
docs = Document("/mnt/lustre/share_data/dist/cmrc2018/data_kb", embed=embed_model)

# 创建自定义节点组：按行分割文档
# transform参数使用lambda函数，将文档按换行符分割成多个文本块
# 这种分割方式适合处理结构化的文本数据
docs.create_node_group(name='block', transform=(lambda d: d.split('\n')))

# ===== 检索器对比配置 =====
# 在相同的节点组上使用不同的相似度算法进行对比实验

# 检索器1：余弦相似度算法
# 特点：基于向量空间模型，计算查询向量与文档向量的夹角余弦值
# 优势：对文档长度不敏感，适合语义相似度匹配
retriever1 = Retriever(docs, group_name="block", similarity="cosine", topk=3)

# 检索器2：BM25中文算法
# 特点：基于词频统计的概率检索模型，专门优化中文处理
# 优势：对关键词匹配敏感，适合精确匹配和关键词检索
retriever2 = Retriever(docs, group_name="block", similarity="bm25_chinese", topk=3)

# ===== 对比实验执行 =====
# 定义测试查询：关于2008年奥运会的问题
query = "都有谁参加了2008年的奥运会？"

# 分别使用两种相似度算法进行检索
result1 = retriever1(query=query)  # 余弦相似度检索结果
result2 = retriever2(query=query)  # BM25中文检索结果

# ===== 结果对比分析 =====
print("=== 相似度算法对比实验结果 ===")
print(f"查询问题: {query}\n")

print("【余弦相似度召回结果】")
print("特点: 基于语义相似度，关注整体语义匹配")
print("-" * 50)
print("\n\n".join([res.get_content() for res in result1]))

print("\n" + "="*60 + "\n")

print("【BM25中文召回结果】")
print("特点: 基于关键词匹配，关注词频和逆文档频率")
print("-" * 50)
print("\n\n".join([res.get_content() for res in result2]))
