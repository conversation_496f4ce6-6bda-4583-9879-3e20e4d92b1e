<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta property="og:type" content="article" />
    <meta property="og:url" content="https://m.bjnews.com.cn/detail/1735095611129204.html" />
    <meta property="og:title" content="外交部驻港公署发言人：惩恶除奸无可指摘，外部干预必将失败" />
    <meta property="og:description" content="外交部驻港公署发言人：惩恶除奸无可指摘，外部干预必将失败" />
    <meta property="og:image" content="https://media.bjnews.com.cn/cover/2024/06/11/5464536306711342410.png?x-oss-process=image/resize,w_200/quality,q_60" />
    <title>外交部驻港公署发言人：惩恶除奸无可指摘，外部干预必将失败</title>
    <!-- <script type="text/javascript" src="https://static.bjnews.com.cn/www/v5/js/jquery-3.3.1.min.js"></script> -->
    <script type="text/javascript" src="https://static.bjnews.com.cn/common/js/jquery.js"></script>
    <!-- <script type="text/javascript" src="https://static.bjnews.com.cn/wap/js/swiper.js"></script>
    <link rel="stylesheet" href="https://static.bjnews.com.cn/wap/css/swiper.css"> -->
    <script type="text/javascript" src="https://static.bjnews.com.cn/wap/js/flexible.min.js?v=2"></script>
    <link rel="stylesheet" href="https://static.bjnews.com.cn/wap/css/indexnewest.css?v=13">
    <script src="https://m.bjnews.com.cn/js/video.min.js"></script>
    <link rel="stylesheet" href="https://m.bjnews.com.cn/css/video-js.min.css">
    <!-- <link href="https://cdn.bootcss.com/photoswipe/4.1.1/photoswipe.css" rel="stylesheet">
    <link href="https://cdn.bootcss.com/photoswipe/4.1.1/default-skin/default-skin.css" rel="stylesheet">
    <script src="https://cdn.bootcss.com/photoswipe/4.1.1/photoswipe.js"></script>
    <script src="https://cdn.bootcss.com/photoswipe/4.1.1/photoswipe-ui-default.js"></script> -->
    <!-- <script src="/js/photoswipe.umd.min.js"></script>
    <script src="/js/photoswipe-lightbox.umd.min.js"></script> -->
    <link rel="stylesheet" href="/css/photoswipe.min.css">
    <link rel="stylesheet" href="/css/default-skin.min.css">
    <script src="/js/photoswipe.min.js"></script>
    <script src="/js/photoswipe-ui-default.min.js"></script>

    <style>
        .rem038{
            font-size:0.38rem !important;
        }
    </style>
    <!-- AdService Head -->
    <!-- <script type="text/javascript" src="https://ad.bjnews.com.cn/js/adservice.js"></script> -->
    <!-- <style>
        .my-video-dimensions {
            width: 100%;
            height: 4.22rem;
        }
        .openapp {
            border-radius:0.08rem;
            display:none;
        }
        .shouy{
            display:none;
        }
        #launch-btn{
            position: relative;
            width:300px;
            height:60px;
            left: 50%;
            /* margin: -30px 0 0 -150px; */
            /* margin-bottom: 45px; */
            padding-bottom: 45px;
            margin-left: -150px;
            display:block;
        }
        .article p{
            min-height:0.5rem;
        }
        .shade{
            text-align:center;
        }
        .shade img{
            margin-top: 0;
            width:0.22rem;
            height:0.22rem;
            animation-play-state:paused;
        }
        .shade .shade-box{
            font-family: PingFangSC-Regular, PingFang SC;
            margin: 0 auto;
            margin-top: 1.4rem;
            font-size:0.3rem;
            font-weight:400;
            color:#D02D2D;
            text-align:center;
            border: 1px solid #D02D2D;
            width:2.19rem;
            height:0.6rem;
            line-height:0.6rem;
            border-radius:0.29rem;
            background:#fff;
        }
        .recommend_c{
            align-items: center;
        }
        .recommend_demo:last-of-type{
            border:none;
        }
        .xjb-top{
            padding:0.13rem 0.3rem;
            display:flex;
            justify-content: space-between;
            background: #F9F9F9;
        }
        .xjb-top-l{
            display:flex;
        }
        .xjb-top-l-logo{
            width:0.74rem;
            height:0.74rem;
            margin-right:0.14rem;
        }
        .xjb-top-l-logo img{
            width:100%;
        }
        .xjb-top-l-js p:first-of-type{
            margin-block-start: 0;
            margin-block-end: 0;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            font-size:0.3rem;
            line-height:1;
            margin-top:0.06rem;
            margin-bottom:0.12rem;
        }
        .xjb-top-l-js p:last-of-type{
            margin-block-start: 0;
            margin-block-end: 0;
            font-size:0.19rem;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #A7A7A7;
            line-height:1;
        }
        .xjb-top-r{
            display:flex;
            align-items: center;
        }
        .xjb-top-r div{
            background: #D02D2D;
            line-height:0.6rem;
            width:1.72rem;
            height:0.6rem;
            /* margin-top:0.06rem; */
            text-align: center;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            font-size:0.28rem;
            border-radius:0.04rem;
        }
        #laun-btn{
            position: relative;
            width:76px;
            height:26px;
            border-radius:0.04rem;
            display:block;
        }
        .xjb-foot{
            background: #F9F9F9;
            padding: 0.5rem 0.3rem 1.28rem 0.3rem;
        }
        .xjb-foot > p{
            font-size: 0.24rem;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #787878;
            line-height:1;
        }
        .xjb-foot p:first-of-type{
            margin-bottom:0.24rem;
        }
        .dingweisy{
            background: url(https://static.bjnews.com.cn/wap/img/syapp.png) no-repeat center;
            background-size:100% 100%;
            width:1rem;
            height:1rem;
            position:fixed;
            /* bottom:2.95rem; */
            bottom:1rem;
            left:0.3rem;
            display:none;
        }
        /* 视频操作框 */
        .article div.vjs-play-progress, .article div.vjs-progress-control, .article div.vjs-volume-panel, .article div.vjs-control-bar, .article span.vjs-icon-placeholder, .article div.vjs-progress-control .vjs-progress-holder{
            font-size: 10px;
            line-height: 1;
        }
        .video-js .vjs-progress-holder .vjs-load-progress, .video-js .vjs-progress-holder .vjs-load-progress div, .video-js .vjs-progress-holder .vjs-play-progress
        .article div.vjs-time-control, .article div.vjs-time-control span, .article span.vjs-control-text, .article span.vjs-remaining-time-display, .article div.vjs-time-tooltip{
            line-height: 3em;
            font-size: 1em;
        }
        .article div.vjs-time-tooltip{
            word-break: normal;
        }
        /* 视频按钮大小 */
        .article .vjs-big-play-centered .vjs-big-play-button{
            width: 0.8em;
            height: 0.8em;
            margin-left: -0.4em;
            border-radius: 100%;
            margin-top: -.6em;
        }
        .article .vjs-big-play-centered .vjs-big-play-button span.vjs-icon-placeholder{
            font-size: 0.6rem;
            line-height: 1.2;
        }
        .article a{
            background-color: transparent;
            color: #4177c1;
        }
        .article .video-js{
            height:4.22rem;
            width:100%;
        }
    </style> -->
</head>

<body>
<input type="hidden" id="uuid" value="1735095611129204">
    <input type="hidden" id="total_words" value="0">
    <div style="width:7.5rem;margin:0 auto;background:#fff;position:relative;">
        <div class="xjb-top">
            <div class="xjb-top-l">
                <div class="xjb-top-l-logo"><img src="https://static.bjnews.com.cn/wap/img/top-logo.png" alt=""></div>
                <div class="xjb-top-l-js">
                    <p>新京报客户端</p>
                    <p>好新闻 无止境</p>
                </div>
            </div>
            <div class="xjb-top-r">
                <div class="shouy dakai">立即打开</div>
                <wx-open-launch-app
                id="laun-btn"
                appid="wx28709be0cee37865"
                extinfo="article/1735095611129204"
                >
                    <template>
                        <style>
                        .btn {
                            width: 100%;
                            height: 26px;
                            line-height: 1;
                            text-align: center;
                            font-size: 12px;
                            border: none;
                            border-radius: 2px;
                            color: #000;
                            background: #D02D2D;
                            color: #FFFFFF;
                            }
                        </style>
                        <button class="btn">立即打开</button>
                    </template>
                </wx-open-launch-app>
            </div>
        </div>
        <!-- 正文部分 -->
        <div class="main">
            <div class="title">外交部驻港公署发言人：惩恶除奸无可指摘，外部干预必将失败</div>
            <!-- <div class="author">新京报 记者 刘平 金城武 编辑 李四 刘宇</div> -->
            <div class="author">新京报 编辑 毛天宇</div>
            <div class="time">
                2024-12-25 11:16            </div>
            <div class="invideo">
                <div class="tips">
                    <a href="/column/9197.html">
                        <img src="https://media.bjnews.com.cn/column/2023/08/11/5353996619979237915.jpg" style="width:.7rem;height:.7rem;border-radius:100%;object-fit:cover">
                    </a>
                    <a href="/column/9197.html" style="color:#333;margin:0 0.2rem;position:relative;top:.1rem" class="dakai">第一看点</a>
                </div>
                <!-- <div class="tips">
                    <span>进入</span>
                    <span style="color:#d02d2d;margin: 0 0.2rem;" class="dakai">第一看点</span>
                    <span>阅读更多内容</span>
                </div> -->
                <div class="subscribe">
                    <div><img src="https://static.bjnews.com.cn/wap/img/dingyue.png" alt=""></div>
                    <div class="subscribe_tit dakai">订阅</div>
                </div>

            </div>

                        <div class="article">
                <div class="article-cen"><p>新京报讯 据外交部驻香港特派员公署官微消息，针对美国、英国、加拿大等国和欧盟的官员、政客发表涉港谬论，妄议香港特区警方依法对郭凤仪等13名海外反中乱港分子采取行动，公然诋毁香港特区法治和“一国两制”实践，干预特区司法，外交部驻港公署发言人表示强烈不满和坚决反对。</p><p><br></p><p>发言人指出，郭凤仪等人长期从事反中乱港活动，鼓吹“港独”，煽动分裂国家、颠覆国家政权，乞求外国对香港特区和有关人士采取制裁行动。他们打着“民主”“人权”的幌子，甘当个别国家乱港遏华的“马前卒”，其违法恶行严重危害国家安全，严重损害香港根本利益，严重冲击“一国两制”原则底线，其行可耻，罪不容赦！</p><p><br></p><p>发言人强调，有法必依、违法必究是法治的核心要义，执法必严、除恶务尽是各国维护国家安全的通行做法。香港特区警方依法对郭等人开展执法行动，是维护法治、顺应民意的正义之举，是捍卫国家主权安全的必要之举，是保障香港长治久安的正当之举，符合国际法和国际通行惯例，天经地义，无可指摘！</p><p><br></p><p>发言人表示，香港是法治社会，普通法制度健全，深得国际社会认同，在“2024年法治指数”排名中维持高位。有关国家无视本国密不透风的国安立法，滥用国安概念，滥施非法的“长臂管辖”和单边制裁打压异己，却对香港国安法根据国际法和国际通例规定的属人管辖和保护性管辖妄加置喙，亵渎法治，伪善双标！</p><p><br></p><p>发言人强调，香港事务纯属中国内政，不容任何外部势力插手。任何外部干预也动摇不了特区依法治港、惩治罪犯的坚定决心，阻挠不了新时代列车“香港号”滚滚向前的车轮！我们严正要求有关国家、组织和政客恪守国际法原则和国际关系基本准则，切实尊重中国主权和香港法治，立即停止无端指责，立即停止干涉香港事务和中国内政。</p><p><br></p><p>编辑 毛天宇</p></div>

                

                <div class="shade">
                    <!-- <img src="https://static.bjnews.com.cn/wap/img/shade.png"> -->
                    <div class="shade-box">展开全文<img src="https://static.bjnews.com.cn/wap/img/jt2.png" alt=""></div>
                </div>
            </div>
            <div class="read">
                <div class="read_img"><img src="https://static.bjnews.com.cn/wap/img/read_img.png"></div>
                <div class="read_demo">
                    <div class="read_tit">全文</div>
                    <div class="read_c">0字</div>
                </div>
                <div class="read_demo1">
                    <div class="read_tit">您已阅读</div>
                    <div class="read_c">%</div>
                </div>
                <div class="read_img"><img src="https://static.bjnews.com.cn/wap/img/read_img.png"></div>
            </div>
            <wx-open-launch-app
            id="launch-btn"
            appid="wx28709be0cee37865"
            extinfo="article/1735095611129204"
            >
                <template>
                    <style>
                    .btn {
                        width: 100%;
                        height: 45px;
                        line-height: 45px;
                        text-align: center;
                        font-size: 16px;
                        border: none;
                        border-radius: 4px;
                        color: #000;
                        background: #D02D2D;
                        color: #FFFFFF;
                        }
                    </style>
                    <button class="btn">打开新京报APP 阅读更多精彩资讯</button>
                </template>
            </wx-open-launch-app>
            <div class="openapp dakai">
                <div class="openapp_tit">打开新京报APP 阅读更多精彩资讯</div>
                <!-- <div class="openapp_img"><img src="https://static.bjnews.com.cn/wap/img/openapp.png"></div> -->
            </div>
        </div>

        <!-- 专题 -->
        
                
        <div class="empty"></div>
        <!-- 相关推荐 -->
                <div class="recommend">
            <div class="recommend_tips">相关推荐</div>
                                        <div class="recommend_demo">
                                <!--  目标文章的uuid：show_uuid-->
                                <input type="hidden" class="uuid_show" value="164273171414914">
                                <input type="hidden" class="timestamp" value="1642731897">


                                <!-- 目标文章的类型：show_type -->
                                <input type="hidden" class="type_show" value="article">
                                <div class="recommend_left">
                                    <div class="recommend_t">
                                        <div class="recommend_tit">驻港公署正告欧洲议会政客：香港海晏河清的大好局面不容干扰破坏</div>
                                    </div>
                                    <div class="recommend_c">
                                        <div class="recommend_column">时事</div>
                                        <div class="recommend_time"></div>
                                    </div>
                                </div>
                                <div class="recommend_right"><img src="https://media.bjnews.com.cn/cover/2020/08/07/4955917348314884246.png" alt=""></div>
                            </div>
                                                    <div class="recommend_demo">
                                <!--  目标文章的uuid：show_uuid-->
                                <input type="hidden" class="uuid_show" value="164078411014924">
                                <input type="hidden" class="timestamp" value="1640820218">


                                <!-- 目标文章的类型：show_type -->
                                <input type="hidden" class="type_show" value="article">
                                <div class="recommend_left">
                                    <div class="recommend_t">
                                        <div class="recommend_tit">外交部驻港公署发言人正告外部干预势力：多行不义必自毙</div>
                                    </div>
                                    <div class="recommend_c">
                                        <div class="recommend_column">第一看点</div>
                                        <div class="recommend_time"></div>
                                    </div>
                                </div>
                                <div class="recommend_right"><img src="https://media.bjnews.com.cn/cover/2021/12/30/5140521478495743268.jpeg" alt=""></div>
                            </div>
                                                    <div class="recommend_demo">
                                <!--  目标文章的uuid：show_uuid-->
                                <input type="hidden" class="uuid_show" value="162451036614279">
                                <input type="hidden" class="timestamp" value="1624510423">


                                <!-- 目标文章的类型：show_type -->
                                <input type="hidden" class="type_show" value="article">
                                <div class="recommend_left">
                                    <div class="recommend_t">
                                        <div class="recommend_tit">驻港公署正告美西方反华政客：休拿新闻自由说事，中方不吃这一套</div>
                                    </div>
                                    <div class="recommend_c">
                                        <div class="recommend_column">时事</div>
                                        <div class="recommend_time"></div>
                                    </div>
                                </div>
                                <div class="recommend_right"><img src="https://media.bjnews.com.cn/cover/2021/06/24/5072113268156343471.jpeg" alt=""></div>
                            </div>
                                                    <div class="recommend_demo">
                                <!--  目标文章的uuid：show_uuid-->
                                <input type="hidden" class="uuid_show" value="157490639015646">
                                <input type="hidden" class="timestamp" value="1574908680">


                                <!-- 目标文章的类型：show_type -->
                                <input type="hidden" class="type_show" value="article">
                                <div class="recommend_left">
                                    <div class="recommend_t">
                                        <div class="recommend_tit">特朗普签署涉港法案 中国多部门再次坚决反对和强烈谴责</div>
                                    </div>
                                    <div class="recommend_c">
                                        <div class="recommend_column">时事</div>
                                        <div class="recommend_time"></div>
                                    </div>
                                </div>
                                <div class="recommend_right"><img src="https://media.bjnews.com.cn/cover/2019/11/28/4864059648354886866.jpg" alt=""></div>
                            </div>
                                                    <div class="recommend_demo">
                                <!--  目标文章的uuid：show_uuid-->
                                <input type="hidden" class="uuid_show" value="162821491414207">
                                <input type="hidden" class="timestamp" value="1628215123">


                                <!-- 目标文章的类型：show_type -->
                                <input type="hidden" class="type_show" value="article">
                                <div class="recommend_left">
                                    <div class="recommend_t">
                                        <div class="recommend_tit">外交部驻港公署正告美方：反中乱港图谋注定失败，趁早死了这条心</div>
                                    </div>
                                    <div class="recommend_c">
                                        <div class="recommend_column">时事</div>
                                        <div class="recommend_time"></div>
                                    </div>
                                </div>
                                <div class="recommend_right"><img src="https://media.bjnews.com.cn/cover/2021/08/06/5087651888267078123.jpg" alt=""></div>
                            </div>
                                                    <div class="recommend_demo">
                                <!--  目标文章的uuid：show_uuid-->
                                <input type="hidden" class="uuid_show" value="161733149215407">
                                <input type="hidden" class="timestamp" value="1617331550">


                                <!-- 目标文章的类型：show_type -->
                                <input type="hidden" class="type_show" value="article">
                                <div class="recommend_left">
                                    <div class="recommend_t">
                                        <div class="recommend_tit">外交部驻港公署正告美驻港官员：立即停止干预香港事务和中国内政</div>
                                    </div>
                                    <div class="recommend_c">
                                        <div class="recommend_column">时事</div>
                                        <div class="recommend_time"></div>
                                    </div>
                                </div>
                                <div class="recommend_right"><img src="https://media.bjnews.com.cn/cover/2020/08/07/4955919649813374947.png" alt=""></div>
                            </div>
                                                    <div class="recommend_demo">
                                <!--  目标文章的uuid：show_uuid-->
                                <input type="hidden" class="uuid_show" value="162434875914320">
                                <input type="hidden" class="timestamp" value="1624348822">


                                <!-- 目标文章的类型：show_type -->
                                <input type="hidden" class="type_show" value="article">
                                <div class="recommend_left">
                                    <div class="recommend_t">
                                        <div class="recommend_tit">驻港公署正告外部势力：新闻自由不是免罪牌，反中乱港没有法外权</div>
                                    </div>
                                    <div class="recommend_c">
                                        <div class="recommend_column">时事</div>
                                        <div class="recommend_time"></div>
                                    </div>
                                </div>
                                <div class="recommend_right"><img src="https://media.bjnews.com.cn/cover/2020/12/08/5000433672143316035.png" alt=""></div>
                            </div>
                                                    <div class="recommend_demo">
                                <!--  目标文章的uuid：show_uuid-->
                                <input type="hidden" class="uuid_show" value="158899533015782">
                                <input type="hidden" class="timestamp" value="1588995410">


                                <!-- 目标文章的类型：show_type -->
                                <input type="hidden" class="type_show" value="article">
                                <div class="recommend_left">
                                    <div class="recommend_t">
                                        <div class="recommend_tit">外交公署：坚决反对内外势力勾结乱港</div>
                                    </div>
                                    <div class="recommend_c">
                                        <div class="recommend_column">时事</div>
                                        <div class="recommend_time"></div>
                                    </div>
                                </div>
                                <div class="recommend_right"><img src="https://media.bjnews.com.cn/cover/2020/05/09/4923152473595653040.png" alt=""></div>
                            </div>
                                    <!-- <div class="openapp1 dakai">
                <div class="openapp1_tit">去APP浏览更多内容</div>
                <div class="openapp_img"><img src="https://static.bjnews.com.cn/wap/img/openapp1.png"></div>
            </div> -->
        </div>
                        <!-- <div class="swiper_b"></div> -->
        <div class="xjb-foot">
            <p>新京报报料邮箱：<EMAIL></p>
        </div>
        <div class="dingweisy"></div>
    </div>

    <!-- 图片查看器 -->
    <!-- Root element of PhotoSwipe. Must have class pswp. -->
    <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
        <!-- Background of PhotoSwipe.
        It's a separate element as animating opacity is faster than rgba(). -->
        <div class="pswp__bg"></div>
        <!-- Slides wrapper with overflow:hidden. -->
        <div class="pswp__scroll-wrap">
            <!-- Container that holds slides.
            PhotoSwipe keeps only 3 of them in the DOM to save memory.
            Don't modify these 3 pswp__item elements, data is added later on. -->
            <div class="pswp__container">
                <div class="pswp__item"></div>
                <div class="pswp__item"></div>
                <div class="pswp__item"></div>
            </div>
            <!-- Default (PhotoSwipeUI_Default) interface on top of sliding area. Can be changed. -->
            <div class="pswp__ui pswp__ui--hidden">
                <div class="pswp__top-bar">
                    <!--  Controls are self-explanatory. Order can be changed. -->
                    <div class="pswp__counter"></div>
                    <button class="pswp__button pswp__button--close" title="Close (Esc)"></button>
                    <!-- <button class="pswp__button pswp__button--share" title="Share"></button>
                    <button class="pswp__button pswp__button--fs" title="Toggle fullscreen"></button>
                    <button class="pswp__button pswp__button--zoom" title="Zoom in/out"></button> -->
                    <!-- Preloader demo http://codepen.io/dimsemenov/pen/yyBWoR -->
                    <!-- element will get class pswp__preloader--active when preloader is running -->
                    <div class="pswp__preloader">
                        <div class="pswp__preloader__icn">
                            <div class="pswp__preloader__cut">
                                <div class="pswp__preloader__donut"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
                    <div class="pswp__share-tooltip"></div>
                </div>
                <button class="pswp__button pswp__button--arrow--left" title="Previous (arrow left)">
                </button>
                <button class="pswp__button pswp__button--arrow--right" title="Next (arrow right)">
                </button>
                <div class="pswp__caption">
                    <div class="pswp__caption__center"></div>
                </div>
            </div>
        </div>
    </div>
<!-- 图片查看器结束 -->

    <!-- <div class="swiper">
        <div class="swiper-container">
            <div class="swiper-wrapper">
                <div class="swiper-slide">
                    <div style="width:5.4rem;display:flex;align-items: center;">
                        <div class="swiper_logo">
                            <img src="https://static.bjnews.com.cn/wap/img/bjnews_app_share2.png">
                        </div>
                        <div class="swiper_log_tit"><img src="https://static.bjnews.com.cn/wap/img/swiper_banner.png"></div>
                    </div>
                </div>
                                            <div class="swiper-slide">
                                <input type="hidden" class="uuid_show" value="1735124804129647">
                                <input type="hidden" class="type_show" value="article">
                                <div class="swiper_img">
                                    <img src="https://media.bjnews.com.cn/cover/2024/12/25/5536064935089481223.jpg" alt="">
                                </div>
                                <div class="swiper_c">
                                    <div class="swiper_tit">邮储银行北京分行：立足五河交汇之畔，服务京津冀协同发展之图</div>

                                </div>
                            </div>
                                                    <div class="swiper-slide">
                                <input type="hidden" class="uuid_show" value="1735125717129675">
                                <input type="hidden" class="type_show" value="article">
                                <div class="swiper_img">
                                    <img src="https://media.bjnews.com.cn/cover/2024/12/25/5536077918393937860.jpg" alt="">
                                </div>
                                <div class="swiper_c">
                                    <div class="swiper_tit">阿塞拜疆总统宣布12月26日为阿航坠机事件哀悼日</div>

                                </div>
                            </div>
                                                    <div class="swiper-slide">
                                <input type="hidden" class="uuid_show" value="1735123238129630">
                                <input type="hidden" class="type_show" value="article">
                                <div class="swiper_img">
                                    <img src="https://media.bjnews.com.cn/cover/2024/12/24/5535696675873563480.png" alt="">
                                </div>
                                <div class="swiper_c">
                                    <div class="swiper_tit">全国人民代表大会常务委员会公告〔十四届〕第八号</div>

                                </div>
                            </div>
                                                    <div class="swiper-slide">
                                <input type="hidden" class="uuid_show" value="1735125680129666">
                                <input type="hidden" class="type_show" value="article">
                                <div class="swiper_img">
                                    <img src="https://media.bjnews.com.cn/cover/2024/12/25/5536067391206794801.jpg" alt="">
                                </div>
                                <div class="swiper_c">
                                    <div class="swiper_tit">新闻8点见丨一架客机在哈萨克斯坦西部坠毁，目前情况如何？</div>

                                </div>
                            </div>
                        
            </div>
            <div class="swiper-pagination"></div>
        </div>
        <div class="swiper_open dakai">
            <p><img src="https://static.bjnews.com.cn/wap/img/dakai.png" alt=""></p>
        </div>
    </div> -->
    <div style="display:none;">
        <script charset="utf-8" language="javascript" src="https://static.bjnews.com.cn/public/tj.js"></script>
    </div>

    <!-- <script src="https://m.bjnews.com.cn/js/video.min.js"></script>
    <link rel="stylesheet" href="https://m.bjnews.com.cn/css/video-js.min.css">  -->
    <script type="text/javascript">
        $(function(){
            var ksginTime = new Date().getTime();
            var imgls = $(".article-cen  img");
            var timer = setInterval(function () {
                var complete = true;
                var endTime = new Date().getTime();
                var timeout = endTime - ksginTime < 50000;
                $(imgls).each(function (i, img) {
                    if (timeout) {
                        complete = complete && img.complete;
                    } else {
                        // clearInterval(timer);
                        $(imgls).each(function (i, img) {
                            if (!img.complete) {
                                imgls.splice(i, 1);
                            }
                        });
                    }
                });
                if (complete) {
                    clearInterval(timer);
                    var items = [];
                    var options = {index: 0, closeOnVerticalDrag: false};
                    var pswpElement = document.querySelectorAll('.pswp')[0];
                    $(imgls).each(function (i, img) {
                        var url = $(this).attr("src");
                        var w = img.naturalWidth;
                        var h = img.naturalHeight;
                        items[i] = {src: url, w: w, h: h};
                        $(this).click(function (e) {
                            options.index = i;
                            var gallery = new PhotoSwipe(pswpElement, PhotoSwipeUI_Default, items, options);
                            gallery.init();
                        });
                    });
                    console.log(items)
                }
            }, 10);
            // const options = {
            //     dataSource: [
            //         // simple image

            //     ],
            //     index:0,
            //     closeOnVerticalDrag: false,
            //     showHideAnimationType: 'none',
            //     history: false, 
            //     focus: false, 
            //     showAnimationDuration: 0, 
            //     hideAnimationDuration: 0,
            //     allowPanToNext: false,
            //     loop: false,
            //     arrowKeys: false,
            //     clickToCloseNonZoomable: false,
            //     errorMsg: 'The photo cannot be loaded',
            //     preloadFirstSlide: true,
            //     // pswpModule: () => import('https://cdn.bootcdn.net/ajax/libs/photoswipe/5.3.6/photoswipe.esm.js')
            //     pswpModule:PhotoSwipe
            // };
            // let tr = false;
            // $('.article img').each(function(t, n) {
            //     if($('.article img').length-1 == t){
            //         tr = true;
            //         return
            //     }
            //     options.dataSource.push({
            //     id: t+1,
            //     src: $(this).attr('src'),
            //     width: $(this).naturalWidth,
            //     height: $(this).naturalHeight,
            //     })
            // })
            // var timer = setInterval(function () {
            //     if(tr){
            //         clearInterval(timer);
            //     }
            //     const lightbox = new PhotoSwipeLightbox(options);
            //     lightbox.init();
            //     $('.article img').click(function(){
            //         // console.log($(this).index(this))
            //         let index = $('.article img').index(this)
            //         options.index = index;
            //         lightbox.loadAndOpen(index)
            //     })
            // },100)
        })
        

        // var web_root_url = "https://m.bjnews.com.cn";
        var web_root_url = "https://m.bjnews.com.cn";
        //获取当前时间戳
        var timesold = new Array()
        var datetime = new Array()
        var obj = $('.timestamp').each(function() {
            var $val = $(this).val()
            // console.log($val)
            timesold.push($val)
        })

        function format(times) {
            if (times) {
                //timestamp是整数，否则要parseInt转换,不会出现少个0的情况
                //计算多久前发布
                var time = new Date(times * 1000);
                // console.log(time)
                var dqtime = new Date();
                var date = dqtime.getTime() - time.getTime()
                date = Math.floor(date / 1000)

                if (date <= 59) {
                    datetime.push(date + '秒前')
                } else if (date <= 3599) {
                    date = Math.floor(date / 60)
                    datetime.push(date + '分钟前')
                } else if (date <= 86399) {
                    date = Math.floor(date / 60 / 60)
                    datetime.push(date + '小时前')
                } else {
                    date = Math.floor(date / 60 / 60 / 24)
                    if (date < 2) {
                        datetime.push('1天前')
                    } else {
                        y = time.getFullYear(); //getFullYear方法以四位数字返回年份
                        M = time.getMonth() + 1; // getMonth方法从 Date 对象返回月份 (0 ~ 11)，返回结果需要手动加一
                        d = time.getDate(); // getDate方法从 Date 对象返回一个月中的某一天 (1 ~ 31)
                        h = time.getHours(); // getHours方法返回 Date 对象的小时 (0 ~ 23)
                        m = time.getMinutes(); // getMinutes方法返回 Date 对象的分钟 (0 ~ 59)
                        s = time.getSeconds(); // getSeconds方法返回 Date 对象的秒数 (0 ~ 59)
                        datetime.push(y + '-' + M + '-' + d)
                        // datetime.push(date + '天前')
                    }

                }
                // var time = new Date(1556610011830);
                // var year = time.getFullYear();
                // var month = time.getMonth()+1;
                // var date = time.getDate();
                // var hours = time.getHours();
                // var minutes = time.getMinutes();
                // var seconds = time.getSeconds();
            } else {
                return '';
            }
        }

        for (i = 0; i < timesold.length; i++) {
            format(timesold[i])
            $('.recommend_time').eq(i).append(datetime[i])
        }
        var height = $(".article").height();
        var uuid = $('#uuid').val();
        var total_words = $('#total_words').val();
        // console.log(total_words)
        if (total_words == 0) {

            $('.read').hide()
            if (height > 800) {
                $(".article").css({
                    'height': '8rem',
                    'overflow': 'hidden',
                })
                $(".shade").css({
                    'display': 'flex',
                })
            }
        } else {

            $('.allarticle').css({
                'display': 'inline-block'
            })
            $('.allarticle').addClass('dakai')
            $('.read').show()
            $('.shade').hide()
            // $('.openapp_tit').text('去APP浏览更多内容')
        }
        $(".shade").click(function() {
            $(".article").css({
                'height': 'auto',
            })
            // $('.openapp_tit').text('去APP浏览更多内容')
            $(".shade").css({
                'display': 'none',
            })
            $('.openapp').hide();
            $('#launch-btn').hide()
        })
        function is_weixn(){
            var ua = navigator.userAgent.toLowerCase();
            if(ua.match(/MicroMessenger/i)=="micromessenger") {
                return true;
            } else {
                return false;
            }
        }
        var is_weixn=is_weixn();
        if(is_weixn){
            $('.shouy').hide()
            $('#laun-btn').show()
            $('.openapp').hide()
            $('#launch-btn').show()
        }else{
            $('.shouy').show()
            $('#laun-btn').hide()
            $('.openapp').css({
                'display':'flex'
            })
            $('#launch-btn').hide()
        }
        $('.dakai').click(function() {
            // tiaoz();

            window.location.href = web_root_url + "/guide/downv2?uuid=1735095611129204&show_type=article&show_uuid=1735095611129204";
        })
        $('.dingweisy').click(function(){
            window.location.href= web_root_url;
        })
        $(window,document,"body").scroll(function(e){
            var scrollTop = $(this).scrollTop();    //滚动条距离顶部的高度
            if(scrollTop > 500){   //距离顶部+当前高度 >=文档总高度 即代表滑动到底部 count++;         //每次滑动count加1
                $('.dingweisy').show();
            }else{
                $('.dingweisy').hide();
            }
        })
        $('.recommend_demo').click(function() {
            var show_uuid = $(this).children('.uuid_show').val();
            // var type_show = $(this).children('.type_show').val();
            window.location.href = web_root_url + "/detail/" + show_uuid + ".html";
        })
        // $('.recommend_demo,.swiper-slide').click(function() {
        //     var show_uuid = $(this).children('.uuid_show').val();
        //     var type_show = $(this).children('.type_show').val();
        //     window.location.href = web_root_url + "/guide/downv2?uuid=1735095611129204&show_type=" + type_show + "&show_uuid=" + show_uuid;
        // })
        // var mySwiper = new Swiper('.swiper-container', {
        //     direction: 'horizontal', // 水平切换选项
        //     loop: true, // 循环模式选项
        //     // 如果需要分页器
        //     pagination: {
        //         el: '.swiper-pagination',
        //     },
        //     speed: 1000,
        //     autoplay: {
        //         delay: 5000, //1秒切换一次
        //     },
        //     disableOnInteraction: false,
        // })
        $(function() {
                        var uuid = $('#uuid').val();
            $.ajax({
                type: 'GET',
               // url: web_root_url + '/api/addpv?uuid=' + uuid,
                 url:  '/api/addpv?uuid=' + uuid,
                dataType: 'JSON',
                success: function(st) {
                    if (st.suc == 1 && st.info == 'ok') {

                    }
                },
                error: function() {}
            })
            
            var referrer = document.referrer;
            if (!referrer) {
                try {
                    if (window.opener) {
                        // IE下如果跨域则抛出权限异常
                        // Safari和Chrome下window.opener.location没有任何属性
                        referrer = window.opener.location.href;
                    }
                } catch (e) {}
            }
            if (referrer) {
                $.post(web_root_url + "/api/waplog", {
                    uuid: $("#uuid").val(),
                    refer: referrer,
                    ua: navigator.userAgent
                }, function(e) {
                    //console.log('waplog:' + e);
                });
            } else {
                //$.post(web_root_url + "/api/waplog", {uuid:'1234567890123', refer:'https://www.baidu.com', ua:'user-agent!'}, function(e){
                //console.log('waplog:' + e);
                //});
            }
            //console.log(navigator.userAgent);



            // 记录分享行为
            var urlSearch = window.location.href.substring(window.location.href.indexOf("?")+1) || "";
            let shareuser = "";
            let shareUrl = ""
            let searchArr = urlSearch.split("&")
            searchArr.forEach((item,index) => {
                let ab = item.split("=");
                if(ab[0] === 'shareuser'){
                    shareuser = ab[1];
                    shareUrl = item;
                }
            })
            // console.log('shareUrl1 ',shareUrl)

            if(shareuser){
                $.ajax({
                    type: 'GET',
                    url: web_root_url + '/japi/pv?article_uuid=' + uuid + '&user_uuid=' + shareuser,
                    dataType: 'JSON',
                    success: function(st) {
                        if (st.suc == 1 && st.info == 'success') {
                        }else{
                        }
                    },
                    error: function() {}
                })
            }
        });
    </script>
    <!-- 与预览页公用 -->
    <script src="https://static.bjnews.com.cn/wap/js/article.js"></script>

    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>

    <!--分享-->
    <script type="text/javascript">
        $.ajax({
            type: "GET",
            url: "https://m.bjnews.com.cn/api/wxshare?app_id=wxbad48ed25d961bb9",
            // url: "https://m.bjnews.com.cn/api/wxshare?app_id=wxbad48ed25d961bb9",
            dataType: 'json',
            success: function(data) {
                var signpackage = data.signpackage;
                wx.config({
                    // debug: true,
                    appId: "wxbad48ed25d961bb9", // 必填，公众号的唯一标识
                    // appId: "wxbad48ed25d961bb9", // 必填，公众号的唯一标识
                    timestamp: signpackage.timestamp, // 必填，生成签名的时间戳
                    nonceStr: signpackage.nonceStr, // 必填，生成签名的随机串
                    signature: signpackage.signature, // 必填，签名，见附录1
                    jsApiList: [
                        'checkJsApi',
                        'onMenuShareTimeline',
                        'onMenuShareAppMessage'
                    ],
                    openTagList: ['wx-open-launch-app']
                });
            }
        });
        wx.ready(function() {

            var share_uuid = "1735095611129204";
            // 获取url分享后缀
            var wxurlSearch = window.location.href.substring(window.location.href.indexOf("?")+1) || "";
            let wxshareuser = "";//url 分享信息
            if(wxurlSearch){
                let wxsearchArr = wxurlSearch.split("&")
                wxsearchArr.forEach((item,index) => {
                    let ab = item.split("=");
                    if(ab[0] === 'shareuser'){
                        wxshareuser = item;
                    }
                })
            }

            // 处理分享链接
            var shareLinkUrl;
            shareLinkUrl = web_root_url + "/detail/" + share_uuid + ".html";
            if (wxshareuser) {
                shareLinkUrl = shareLinkUrl + "?" + wxshareuser;
            }

            //分享到朋友圈
            wx.onMenuShareTimeline({
                title: "外交部驻港公署发言人：惩恶除奸无可指摘，外部干预必将失败", // 分享标题
                link: shareLinkUrl, //分享链接
                //imgUrl: "https://static.bjnews.com.cn/wap/img/bjnews_app_share1.png", // 分享图标
                imgUrl: "https://media.bjnews.com.cn/cover/2024/06/11/5464536306711342410.png?x-oss-process=image/resize,m_fill,h_300,w_300", // 分享图标
                success: function() {
                    // 用户确认分享后执行的回调函数
                    addShare(share_uuid);
                }
            });
            //分享给朋友
            wx.onMenuShareAppMessage({
                title: "外交部驻港公署发言人：惩恶除奸无可指摘，外部干预必将失败", // 分享标题
                desc: "新京报客户端", // 分享描述
                //imgUrl: "https://static.bjnews.com.cn/wap/img/bjnews_app_share1.png", // 分享图标
                imgUrl: "https://media.bjnews.com.cn/cover/2024/06/11/5464536306711342410.png?x-oss-process=image/resize,m_fill,h_300,w_300", // 分享图标
                link:  shareLinkUrl, // 分享链接
                success: function() {
                    // 用户确认分享后执行的回调函数
                    addShare(share_uuid);
                }
            });

        });
        var btn = document.getElementById('launch-btn');
        btn.addEventListener('launch', function (e) {
            // alert('lanuch成功');
        });
        btn.addEventListener('error', function (e) {
            // alert($.param(e.detail))
            window.location.href = web_root_url + "/guide/downv2?uuid=1735095611129204&show_type=article&show_uuid=1735095611129204";
        });
        var bt = document.getElementById('laun-btn');
        bt.addEventListener('launch', function (e) {
            // alert('lanuch成功');
        });
        bt.addEventListener('error', function (e) {
            // alert($.param(e.detail))
            window.location.href = web_root_url + "/guide/downv2?uuid=1735095611129204&show_type=article&show_uuid=1735095611129204";
        });
        //http://media.bjnews.com.cn/cover/2018/10/23/4718848542783908868.jpg?x-oss-process=image/resize,m_fill,h_300,w_300
        function addShare(uuid) {
            $.ajax({
                type: "GET",
                url: web_root_url + "/api/addshare?uuid=" + uuid,
                dataType: 'json',
                success: function(data) {
                    //...
                }
            });
        }
    </script>
</body>

</html>
