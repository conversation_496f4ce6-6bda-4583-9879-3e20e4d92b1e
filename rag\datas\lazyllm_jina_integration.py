#!/usr/bin/env python3
"""
LazyLLM 与 Jina 嵌入集成示例
展示如何在 LazyLLM RAG 系统中使用自定义的 Jina 嵌入模块
"""

import logging
from pathlib import Path
from datasets import load_dataset

# 导入自定义模块
from jina_embedding import createJinaEmbedding
from data_pre import KnowledgeBaseBuilder, KnowledgeBaseConfig
from config import config

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LazyLLMJinaRAG:
    """
    集成 Jina 嵌入的 LazyLLM RAG 系统
    """
    
    def __init__(self, 
                 jina_api_key: str = None,
                 kb_config: KnowledgeBaseConfig = None):
        """
        初始化 RAG 系统
        
        Args:
            jina_api_key: Jina API 密钥
            kb_config: 知识库配置
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 使用配置文件中的 API 密钥或传入的密钥
        self.jina_api_key = jina_api_key or config.JINA_API_KEY
        if not self.jina_api_key:
            raise ValueError("必须提供 Jina API 密钥")
        
        # 知识库配置
        self.kb_config = kb_config or KnowledgeBaseConfig(
            chunk_size=config.DEFAULT_CHUNK_SIZE,
            output_dir=config.DEFAULT_OUTPUT_DIR,
            file_prefix=config.DEFAULT_FILE_PREFIX
        )
        
        # 初始化组件
        self.kb_builder = None
        self.jina_embedding = None
        self.knowledge_base_files = []
        self.embeddings_cache = {}
        
        self.logger.info("LazyLLM Jina RAG 系统初始化完成")
    
    def setupJinaEmbedding(self):
        """设置 Jina 嵌入模块"""
        try:
            self.jina_embedding = createJinaEmbedding(
                api_key=self.jina_api_key,
                model=config.JINA_MODEL,
                task=config.JINA_TASK,
                batch_size=20  # 适中的批量大小
            )
            self.logger.info("Jina 嵌入模块设置完成")
        except Exception as e:
            self.logger.error(f"Jina 嵌入模块设置失败：{e}")
            raise
    
    def setupKnowledgeBase(self):
        """设置知识库构建器"""
        try:
            self.kb_builder = KnowledgeBaseBuilder(self.kb_config)
            self.logger.info("知识库构建器设置完成")
        except Exception as e:
            self.logger.error(f"知识库构建器设置失败：{e}")
            raise
    
    def buildKnowledgeBase(self, dataset):
        """
        构建知识库
        
        Args:
            dataset: 数据集
            
        Returns:
            创建的文件列表
        """
        try:
            self.logger.info("开始构建知识库...")
            
            if not self.kb_builder:
                self.setupKnowledgeBase()
            
            # 创建知识库文件
            self.knowledge_base_files = self.kb_builder.createKnowledgeBase(dataset)
            
            self.logger.info(f"知识库构建完成，共创建 {len(self.knowledge_base_files)} 个文件")
            return self.knowledge_base_files
            
        except Exception as e:
            self.logger.error(f"知识库构建失败：{e}")
            raise
    
    def generateEmbeddings(self, max_files: int = None):
        """
        为知识库内容生成嵌入向量
        
        Args:
            max_files: 最大处理文件数（用于测试）
            
        Returns:
            嵌入信息字典
        """
        try:
            if not self.knowledge_base_files:
                raise ValueError("知识库文件未创建，请先调用 buildKnowledgeBase")
            
            if not self.jina_embedding:
                self.setupJinaEmbedding()
            
            self.logger.info("开始为知识库内容生成嵌入向量...")
            
            # 读取文件内容
            all_texts = []
            file_text_mapping = {}
            
            files_to_process = self.knowledge_base_files
            if max_files:
                files_to_process = files_to_process[:max_files]
            
            for file_path in files_to_process:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        texts = [line.strip() for line in content.split('\n') if line.strip()]
                        all_texts.extend(texts)
                        file_text_mapping[str(file_path)] = texts
                except Exception as e:
                    self.logger.warning(f"读取文件 {file_path} 失败：{e}")
                    continue
            
            if not all_texts:
                raise ValueError("没有有效的文本内容")
            
            self.logger.info(f"准备为 {len(all_texts)} 条文本生成嵌入向量...")
            
            # 生成嵌入向量
            embeddings = self.jina_embedding.embed(all_texts)
            
            # 缓存嵌入结果
            embedding_info = {
                'texts': all_texts,
                'embeddings': embeddings,
                'file_mapping': file_text_mapping,
                'vector_dim': len(embeddings[0]) if embeddings else 0,
                'total_vectors': len(embeddings)
            }
            
            self.embeddings_cache = embedding_info
            
            self.logger.info(f"嵌入向量生成完成：{len(embeddings)} 个向量，维度：{len(embeddings[0])}")
            return embedding_info
            
        except Exception as e:
            self.logger.error(f"生成嵌入向量失败：{e}")
            raise
    
    def searchSimilarTexts(self, query: str, top_k: int = 5):
        """
        搜索相似文本
        
        Args:
            query: 查询文本
            top_k: 返回最相似的文本数量
            
        Returns:
            相似文本列表
        """
        try:
            if not self.embeddings_cache:
                raise ValueError("嵌入向量未生成，请先调用 generateEmbeddings")
            
            if not self.jina_embedding:
                self.setupJinaEmbedding()
            
            self.logger.info(f"搜索与查询相似的文本：{query[:50]}...")
            
            # 生成查询嵌入向量
            query_embedding = self.jina_embedding.embed(query)
            
            # 计算相似度
            import numpy as np
            
            texts = self.embeddings_cache['texts']
            embeddings = np.array(self.embeddings_cache['embeddings'])
            query_vec = np.array(query_embedding)
            
            # 计算余弦相似度
            similarities = []
            for i, embedding in enumerate(embeddings):
                # 余弦相似度
                dot_product = np.dot(query_vec, embedding)
                norm_a = np.linalg.norm(query_vec)
                norm_b = np.linalg.norm(embedding)
                similarity = dot_product / (norm_a * norm_b) if norm_a > 0 and norm_b > 0 else 0
                similarities.append((i, similarity, texts[i]))
            
            # 排序并返回最相似的文本
            similarities.sort(key=lambda x: x[1], reverse=True)
            top_results = similarities[:top_k]
            
            self.logger.info(f"找到 {len(top_results)} 个相似文本")
            
            return [
                {
                    'index': idx,
                    'similarity': sim,
                    'text': text,
                    'preview': text[:100] + '...' if len(text) > 100 else text
                }
                for idx, sim, text in top_results
            ]
            
        except ImportError:
            self.logger.error("numpy 未安装，无法计算相似度")
            raise
        except Exception as e:
            self.logger.error(f"搜索相似文本失败：{e}")
            raise
    
    def demonstrateRAG(self, query: str):
        """
        演示完整的 RAG 流程
        
        Args:
            query: 查询问题
        """
        try:
            self.logger.info(f"演示 RAG 流程，查询：{query}")
            
            # 1. 检索相似文本
            similar_texts = self.searchSimilarTexts(query, top_k=3)
            
            # 2. 构建上下文
            context = "\n".join([item['text'] for item in similar_texts])
            
            # 3. 显示结果
            self.logger.info("检索到的相关文本：")
            for i, item in enumerate(similar_texts, 1):
                self.logger.info(f"  {i}. 相似度: {item['similarity']:.4f}")
                self.logger.info(f"     内容: {item['preview']}")
            
            # 4. 构建 RAG 提示（这里只是示例，实际需要调用 LLM）
            rag_prompt = f"""
基于以下上下文信息回答问题：

上下文：
{context}

问题：{query}

请基于上下文信息给出准确的回答。
"""
            
            self.logger.info("构建的 RAG 提示：")
            self.logger.info(rag_prompt[:500] + "..." if len(rag_prompt) > 500 else rag_prompt)
            
            return {
                'query': query,
                'similar_texts': similar_texts,
                'context': context,
                'rag_prompt': rag_prompt
            }
            
        except Exception as e:
            self.logger.error(f"RAG 演示失败：{e}")
            raise

def main():
    """主函数：完整的 RAG 系统演示"""
    logger.info("="*60)
    logger.info("LazyLLM 与 Jina 嵌入集成演示")
    logger.info("="*60)
    
    try:
        # 1. 初始化 RAG 系统
        logger.info("\n1. 初始化 RAG 系统")
        logger.info("-" * 40)
        
        rag_system = LazyLLMJinaRAG()
        
        # 2. 加载数据集
        logger.info("\n2. 加载数据集")
        logger.info("-" * 40)
        
        dataset = load_dataset('cmrc2018')
        logger.info(f"数据集加载完成：{list(dataset.keys())}")
        
        # 3. 构建知识库
        logger.info("\n3. 构建知识库")
        logger.info("-" * 40)
        
        kb_files = rag_system.buildKnowledgeBase(dataset['test'])
        
        # 4. 生成嵌入向量
        logger.info("\n4. 生成嵌入向量")
        logger.info("-" * 40)
        
        embedding_info = rag_system.generateEmbeddings(max_files=5)  # 只处理前5个文件
        
        # 5. 演示 RAG 查询
        logger.info("\n5. 演示 RAG 查询")
        logger.info("-" * 40)
        
        test_queries = [
            "什么是人工智能？",
            "机器学习的应用有哪些？",
            "深度学习和传统机器学习的区别是什么？"
        ]
        
        for query in test_queries:
            logger.info(f"\n查询：{query}")
            logger.info("-" * 20)
            rag_result = rag_system.demonstrateRAG(query)
        
        logger.info("\n" + "="*60)
        logger.info("RAG 系统演示完成！")
        logger.info("="*60)
        
        # 输出总结
        logger.info(f"\n总结：")
        logger.info(f"- 知识库文件：{len(kb_files)} 个")
        logger.info(f"- 嵌入向量：{embedding_info['total_vectors']} 个")
        logger.info(f"- 向量维度：{embedding_info['vector_dim']}")
        
    except Exception as e:
        logger.error(f"RAG 系统演示失败：{e}")
        raise

if __name__ == "__main__":
    main()
