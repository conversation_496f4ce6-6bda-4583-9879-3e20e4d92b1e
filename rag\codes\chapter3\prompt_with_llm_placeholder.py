import lazyllm

llm2 = lazyllm.OnlineChatModule(source="openai",
    model="ep-20250822223253-s98w6",
    base_url="https://ark.cn-beijing.volces.com/api/v3/",
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
    stream=True).prompt("根据给出的文段回答问题，文段：{content}")

passage = ('孙悟空，是在小说《西游记》当中唐僧的四个徒弟之一，排行第一，别名孙行者、孙猴子。'
           '自封美猴王、齐天大圣。因曾在天庭掌管御马监而又被称为弼马温，在取经完成后被如来佛祖授封为斗战胜佛')
prompt_content = llm2._prompt.generate_prompt({'input':'孙悟空有哪些名字？', 'content':passage}, return_dict=True)
print("===prompt_content===",prompt_content)
print(llm2({'input':'孙悟空有哪些名字？', 'content':passage}))