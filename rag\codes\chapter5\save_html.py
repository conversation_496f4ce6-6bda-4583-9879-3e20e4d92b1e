# 导入HTTP请求库
import requests

# 目标网页URL - CSDN博客文章
url = "https://blog.csdn.net/star_nwe/article/details/141174167"

# 设置请求头，模拟浏览器访问
# User-Agent用于避免被网站识别为爬虫而被拒绝访问
headers = {
    "User-Agent": ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                   "Chrome/131.0.0.0 Safari/537.36")
}

# 发送GET请求获取网页内容
response = requests.get(url, headers=headers)

# 检查请求是否成功
if response.status_code == 200:
    # 将网页内容保存为HTML文件
    # 使用UTF-8编码确保中文内容正确保存
    with open("webPage.html", "w", encoding='utf-8') as file:
        file.write(response.text)
    print("Webpage downloaded successfully!")
else:
    # 请求失败时输出错误信息
    print(f"Failed to download webpage. Status code: {response.status_code}")
