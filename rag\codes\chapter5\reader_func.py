# 导入LazyLLM RAG工具包中的文档节点类
from lazyllm.tools.rag import DocNode
# 导入BeautifulSoup用于HTML解析
from bs4 import BeautifulSoup
# 导入LazyLLM RAG工具包中的文档管理类
from lazyllm.tools.rag import Document


def processHtml(file, extra_info=None):
    """
    HTML文件处理函数

    功能：将HTML文件转换为RAG系统可处理的文档节点格式

    参数:
        file (str): HTML文件的路径
        extra_info (dict, optional): 额外的元数据信息，默认为None

    返回:
        list: 包含DocNode对象的列表

    处理流程:
        1. 读取HTML文件内容
        2. 使用BeautifulSoup解析HTML
        3. 提取纯文本内容（去除HTML标签）
        4. 创建文档节点对象
    """
    text = ''  # 初始化文本变量

    # 以UTF-8编码打开并读取HTML文件
    with open(file, 'r', encoding='utf-8') as f:
        data = f.read()  # 读取文件全部内容

        # 使用BeautifulSoup解析HTML，指定lxml解析器
        soup = BeautifulSoup(data, 'lxml')

        # 遍历所有去除空白的字符串元素，提取纯文本
        for element in soup.stripped_strings:
            text += element + '\n'  # 每个元素后添加换行符

    # 创建文档节点，包含提取的文本和元数据
    node = DocNode(text=text, metadata=extra_info or {})
    return [node]  # 返回包含节点的列表


# 使用示例：演示如何使用自定义HTML处理函数
# 创建文档对象，指定数据集路径
doc = Document(dataset_path="datas")

# 为HTML文件类型注册自定义处理函数
# "*.html" 表示匹配所有.html文件，processHtml是处理函数
doc.add_reader("*.html", processHtml)

# 加载指定的HTML文件并处理
# 注意：需要确保webPage.html文件存在于指定路径
data = doc._impl._reader.load_data(input_files=["webPage.html"])

# 输出处理结果
print(f"data: {data}")  # 打印整个数据对象
print(f"text: {data[0].text}")  # 打印第一个文档节点的文本内容
