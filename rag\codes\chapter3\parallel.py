# 导入LazyLLM主模块
import lazyllm

# 定义三个简单的数学运算函数
test1 = lambda a: a + 1  # 加1操作
test2 = lambda a: a * 4  # 乘4操作
test3 = lambda a: a / 2  # 除2操作

# 创建不同输出格式的并行处理器
# 1. 默认输出格式（列表）
prl1 = lazyllm.parallel(test1, test2, test3)

# 2. 输出为字典格式，使用命名参数
prl2 = lazyllm.parallel(path1=test1, path2=test2, path3=test3).asdict

# 3. 输出为元组格式
prl3 = lazyllm.parallel(test1, test2, test3).astuple

# 4. 输出为列表格式（显式指定）
prl4 = lazyllm.parallel(test1, test2, test3).aslist

# 5. 输出为字符串格式，使用指定分隔符连接
prl5 = lazyllm.parallel(test1, test2, test3).join('，')

# 测试不同输出格式的并行处理结果
# 输入值为1，三个函数的计算结果分别为：1+1=2, 1*4=4, 1/2=0.5
print("默认输出：prl1(1) -> ", prl1(1), type(prl1(1)))      # [2, 4, 0.5]
print("输出字典：prl2(1) -> ", prl2(1), type(prl2(1)))      # {'path1': 2, 'path2': 4, 'path3': 0.5}
print("输出元组：prl3(1) -> ", prl3(1), type(prl3(1)))      # (2, 4, 0.5)
print("输出列表：prl4(1) -> ", prl4(1), type(prl4(1)))      # [2, 4, 0.5]
print("输出字符串：prl5(1) -> ", prl5(1), type(prl5(1)))    # "2，4，0.5"