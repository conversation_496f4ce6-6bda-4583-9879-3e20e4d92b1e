# 导入LazyLLM主模块
import lazyllm

# ===== 模型配置部分 =====

# 定义嵌入模型：用于将文本转换为向量表示
# 可选择在线嵌入模型或本地嵌入模型
# embedding_model = lazyllm.OnlineEmbeddingModule()  # 在线嵌入模型选项
embedding_model = lazyllm.TrainableModule("bge-large-zh-v1.5").start()  # 本地中文嵌入模型

# 定义重排序模型：用于对检索结果进行重新排序
# 在线重排序模型选项（需要API密钥）
# 目前LazyLLM仅支持 qwen和glm 在线重排模型，请指定相应的 API key
# online_rerank = lazyllm.OnlineEmbeddingModule(type="rerank")

# 本地重排序模型：BGE重排序模型，用于提高检索精度
offline_rerank = lazyllm.TrainableModule('bge-reranker-large').start()

# ===== 文档处理部分 =====

# 创建文档对象，指定数据集路径和嵌入模型
docs = lazyllm.Document("/mnt/lustre/share_data/dist/cmrc2018/data_kb", embed=embedding_model)

# 创建自定义节点组：按行分割文档
# transform参数使用lambda函数，将文档按换行符分割成多个块
docs.create_node_group(name='block', transform=(lambda d: d.split('\n')))

# ===== 检索器配置部分 =====

# 定义检索器1：基于粗粒度块的余弦相似度检索
# group_name: 使用内置的"CoarseChunk"节点组
# similarity: 使用余弦相似度算法
# topk: 返回前3个最相关的文档
retriever1 = lazyllm.Retriever(docs, group_name="CoarseChunk", similarity="cosine", topk=3)

# 定义检索器2：基于自定义块的BM25中文检索
# group_name: 使用自定义的"block"节点组
# similarity: 使用BM25中文算法
# topk: 返回前3个最相关的文档
retriever2 = lazyllm.Retriever(docs, group_name="block", similarity="bm25_chinese", topk=3)

# ===== 重排序器配置部分 =====

# 定义重排序器：使用模块化重排序器提高检索精度
# 'ModuleReranker': 指定重排序器类型
# model: 使用本地重排序模型
# topk: 最终返回前3个最相关的文档
reranker = lazyllm.Reranker('ModuleReranker', model=offline_rerank, topk=3)

# ===== 大语言模型配置部分 =====

# 定义大语言模型：使用InternLM2-Chat-20B模型
# deploy_method: 使用Vllm部署方法提高推理效率
llm = lazyllm.TrainableModule('internlm2-chat-20b').deploy_method(lazyllm.deploy.Vllm).start()

# 设计提示词模板
prompt = '你是一个友好的 AI 问答助手，你需要根据给定的上下文和问题提供答案。\
          根据以下资料回答问题：\
          {context_str} \n '
# 配置聊天提示器，指定额外的上下文键
llm.prompt(lazyllm.ChatPrompter(instruction=prompt, extro_keys=['context_str']))

# ===== RAG推理执行部分 =====

# 定义查询问题
query = "2008年有哪些赛事？"

# 执行多路检索：使用两个不同的检索器
result1 = retriever1(query=query)  # 余弦相似度检索结果
result2 = retriever2(query=query)  # BM25中文检索结果

# 合并检索结果并进行重排序
# 将两个检索器的结果合并，然后使用重排序器优化排序
result = reranker(result1+result2, query=query)

# 构建大模型输入并生成最终答案
# 将查询和重排序后的文档内容组合成字典格式
res = llm({"query": query, "context_str": "".join([node.get_content() for node in result])})

# 输出最终答案
print(f'Answer: {res}')
