# 导入LazyLLM RAG读取器基类
from lazyllm.tools.rag.readers import ReaderBase
# 导入设备推断工具
from lazyllm.tools.rag.readers.readerBase import infer_torch_device
# 导入文档节点类
from lazyllm.tools.rag import DocNode
# 导入路径处理模块
from pathlib import Path
# 导入类型提示
from typing import Optional, Dict, List
# 导入PIL图像处理库
from PIL import Image
# 导入Transformers库中的BLIP模型和处理器
from transformers import BlipForConditionalGeneration, BlipProcessor
# 导入PyTorch深度学习框架
import torch
# 导入LazyLLM文档管理类
from lazyllm.tools.rag import Document


class ImageDescriptionReader(ReaderBase):
    """
    图像描述读取器类
    功能：使用BLIP模型为图像生成文本描述，将图像转换为可搜索的文本内容
    继承自ReaderBase，实现图像文件的自动描述生成
    """
    def __init__(self, parser_config: Optional[Dict] = None, prompt: Optional[str] = None) -> None:
        """
        初始化图像描述读取器

        参数:
            parser_config: 解析器配置字典，包含模型、处理器等配置
            prompt: 可选的提示词，用于指导图像描述生成
        """
        super().__init__()
        if parser_config is None:
            # 自动推断最佳计算设备（GPU或CPU）
            device = infer_torch_device()
            # 根据CUDA可用性选择数据类型，GPU使用float16，CPU使用float32
            dtype = torch.float16 if torch.cuda.is_available() else torch.float32
            # 加载BLIP图像描述处理器
            processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-large")
            # 加载BLIP图像描述生成模型
            model = BlipForConditionalGeneration.from_pretrained("Salesforce/blip-image-captioning-large",
                                                                 torch_dtype=dtype)
            # 构建默认解析器配置
            parser_config = {"processor": processor, "model": model, "device": device, "dtype": dtype}
        self._parser_config = parser_config
        self._prompt = prompt

    def _load_data(self, file: Path, extra_info: Optional[Dict] = None) -> List[DocNode]:
        """
        加载并处理图像文件，生成文本描述

        参数:
            file: 图像文件路径
            extra_info: 额外的元数据信息

        返回:
            包含图像描述文本的DocNode列表
        """
        # 使用PIL打开图像文件
        image = Image.open(file)
        # 确保图像为RGB模式，BLIP模型要求RGB格式
        if image.mode != "RGB":
            image = image.convert("RGB")

        # 获取配置中的模型和处理器
        model = self._parser_config['model']
        processor = self._parser_config["processor"]

        # 获取设备和数据类型配置
        device = self._parser_config["device"]
        dtype = self._parser_config["dtype"]
        # 将模型移动到指定设备
        model.to(device)

        # 使用处理器预处理图像和提示词，转换为模型输入格式
        inputs = processor(image, self._prompt, return_tensors="pt").to(device, dtype)

        # 使用模型生成图像描述
        out = model.generate(**inputs)
        # 解码生成的文本，跳过特殊标记
        text_str = processor.decode(out[0], skip_special_tokens=True)
        # 创建并返回文档节点
        return [DocNode(text=text_str, metadata=extra_info or {})]


# 使用示例：演示如何使用自定义图像描述读取器
# 创建文档对象
doc = Document(dataset_path="your_doc_path")
# 为PNG文件注册图像描述读取器
doc.add_reader("*.png", ImageDescriptionReader)
# 加载并处理指定的PNG图像文件
data = doc._impl._reader.load_data(input_files=["cmrc2018_path/LazyLLM-logo.png"])
# 输出处理结果
print(f"data: {data}")
print(f"text: {data[0].text}")
