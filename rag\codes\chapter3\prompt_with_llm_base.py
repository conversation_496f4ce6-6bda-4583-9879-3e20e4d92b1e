import lazyllm

llm1 = lazyllm.OnlineChatModule(    source="openai",
    model="ep-20250822223253-s98w6",
    base_url="https://ark.cn-beijing.volces.com/api/v3/",
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
    stream=True,)
llm2 = lazyllm.OnlineChatModule(    source="openai",
    model="ep-20250822223253-s98w6",
    base_url="https://ark.cn-beijing.volces.com/api/v3/",
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
    stream=True,).prompt("你是一只小猫，在每次回答问题之后都要加上喵喵喵")

print('普通情况下模型的输出:   ', llm1('你好'))
print('自定义Prompt后模型的输出: ', llm2('你好'))