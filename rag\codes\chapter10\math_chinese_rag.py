# 导入LazyLLM相关模块
import lazyllm
from lazyllm import bind
from lazyllm.tools import IntentClassifier

# ===== 中文数学RAG系统 =====
# 结合意图分类和RAG技术，专门处理中文数学问题

# 定义问答模板
# 用于格式化上下文和问题，确保模型理解任务要求
template = "请用下面的文段的原文来回答问题\n\n### 已知文段：{context}\n\n### 问题：{question}\n"

# ===== 数学专用模型配置 =====
# 使用专门针对中文数学问题微调的InternLM2模型
base_model = 'path/to/internlm2-chat-7b-chinese-math2'
base_llm = lazyllm.TrainableModule(base_model)

# ===== 文档知识库配置 =====
# 加载CMRC2018中文阅读理解数据集作为知识库
documents = lazyllm.Document(dataset_path="path/to/cmrc2018/data_kb")

# ===== RAG流水线构建 =====
# 构建检索增强生成流水线，用于处理需要外部知识的问题
with lazyllm.pipeline() as ppl:
    # 检索组件：使用BM25中文算法进行文档检索
    # group_name: 使用粗粒度文档块
    # similarity: BM25中文算法，适合关键词匹配
    # topk: 返回前3个最相关的文档
    ppl.retriever = lazyllm.Retriever(doc=documents, group_name="CoarseChunk", similarity="bm25_chinese", topk=3)

    # 格式化组件：将检索到的文档和查询格式化为模板
    # lambda函数：接收检索节点和查询，返回格式化的提示词
    # bind: 将查询绑定到流水线输入
    ppl.formatter = (lambda nodes, query: template.format(
        context="".join([node.get_content() for node in nodes]), question=query)) | bind(query=ppl.input)

    # 生成组件：使用数学专用模型生成答案
    ppl.llm = base_llm

# ===== 意图分类系统 =====
# 使用意图分类器区分数学问题和一般问题，选择合适的处理方式
with IntentClassifier(lazyllm.OnlineChatModule()) as ic:
    # 数学问题分支：直接使用数学专用模型
    # 适用于纯数学计算、公式推导等不需要外部知识的问题
    ic.case['Math', base_llm]

    # 默认分支：使用RAG流水线
    # 适用于需要结合外部知识回答的问题
    ic.case['Default', ppl]

# ===== Web服务启动 =====
# 启动Web服务，提供HTTP接口访问
# port: 指定服务端口为23496
# start(): 启动服务
# wait(): 保持服务运行状态
lazyllm.WebModule(ic, port=23496).start().wait()

# ===== 系统工作流程说明 =====
# 1. 用户提交问题
# 2. 意图分类器判断问题类型
# 3. 如果是数学问题 -> 直接使用数学专用模型
# 4. 如果是其他问题 -> 使用RAG流水线（检索+生成）
# 5. 返回最终答案
