#!/usr/bin/env python3
"""
知识库构建使用示例
演示如何使用优化后的 KnowledgeBaseBuilder 类
"""

import logging
from datasets import load_dataset
from data_pre import KnowledgeBaseBuilder, KnowledgeBaseConfig, displayFileContent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def loadCmrc2018Dataset():
    """
    加载 CMRC2018 数据集
    
    Returns:
        加载的数据集
    """
    try:
        logger.info("正在加载 CMRC2018 数据集...")
        dataset = load_dataset('cmrc2018')
        logger.info(f"数据集加载成功，包含：{list(dataset.keys())}")
        return dataset
    except Exception as e:
        logger.error(f"加载数据集失败：{e}")
        raise

def createKnowledgeBaseExample():
    """
    创建知识库的完整示例
    """
    try:
        # 1. 加载数据集
        dataset = loadCmrc2018Dataset()
        
        # 2. 创建自定义配置
        config = KnowledgeBaseConfig(
            chunk_size=10,           # 每个文件包含10条数据
            output_dir="data_kb",    # 输出目录
            file_prefix="part",      # 文件前缀
            encoding="utf-8"         # 文件编码
        )
        
        # 3. 创建知识库构建器
        kb_builder = KnowledgeBaseBuilder(config)
        
        # 4. 创建知识库
        logger.info("开始创建知识库...")
        created_files = kb_builder.createKnowledgeBase(dataset['test'])
        
        # 5. 显示结果
        if created_files:
            logger.info(f"知识库创建成功！共创建了 {len(created_files)} 个文件：")
            for file_path in created_files:
                logger.info(f"  - {file_path}")
            
            # 显示第一个文件的内容
            logger.info("\n" + "="*50)
            logger.info("第一个文件内容预览：")
            displayFileContent(str(created_files[0]), max_lines=10)
        else:
            logger.warning("没有创建任何文件")
            
    except Exception as e:
        logger.error(f"创建知识库示例失败：{e}")
        raise

def createCustomConfigExample():
    """
    使用自定义配置的示例
    """
    try:
        # 加载数据集
        dataset = loadCmrc2018Dataset()
        
        # 创建自定义配置 - 更小的chunk size
        custom_config = KnowledgeBaseConfig(
            chunk_size=5,                    # 每个文件只包含5条数据
            output_dir="custom_kb",          # 自定义输出目录
            file_prefix="custom_part",       # 自定义文件前缀
            encoding="utf-8"
        )
        
        # 使用自定义配置创建知识库
        kb_builder = KnowledgeBaseBuilder(custom_config)
        created_files = kb_builder.createKnowledgeBase(dataset['test'])
        
        logger.info(f"使用自定义配置创建了 {len(created_files)} 个文件")
        
    except Exception as e:
        logger.error(f"自定义配置示例失败：{e}")

def main():
    """主函数"""
    logger.info("开始知识库构建示例...")
    
    try:
        # 示例1：基本使用
        logger.info("\n" + "="*60)
        logger.info("示例1：基本知识库创建")
        logger.info("="*60)
        createKnowledgeBaseExample()
        
        # 示例2：自定义配置
        logger.info("\n" + "="*60)
        logger.info("示例2：使用自定义配置")
        logger.info("="*60)
        createCustomConfigExample()
        
        logger.info("\n所有示例执行完成！")
        
    except Exception as e:
        logger.error(f"示例执行失败：{e}")

if __name__ == "__main__":
    main()
