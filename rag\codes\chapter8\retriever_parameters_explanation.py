# 导入LazyLLM相关模块
from lazyllm import OnlineEmbeddingModule, SentenceSplitter, Retriever
from lazyllm.tools.rag import Document

# ===== 检索器参数详解示例 =====
# 本文件演示Retriever组件的各种参数配置和输出格式

# 定义AI助手提示词（用于后续可能的LLM集成）
prompt = ('You will play the role of an AI Q&A assistant and complete a dialogue task. '
          'In this task, you need to provide your answer based on the given context and question.')

# ===== 文档和嵌入配置 =====

# 创建文档对象
# dataset_path: 指定数据集路径
# embed: 使用GLM的在线嵌入模型embedding-2
# manager: 设置为False，不使用文档管理器
documents = Document(dataset_path="rag_master",
                     embed=OnlineEmbeddingModule(source="glm", embed_model_name="embedding-2"), manager=False)

# 创建句子级别的节点组
# name: 节点组名称
# transform: 使用SentenceSplitter进行句子分割
# chunk_size: 每个文档块的大小为1024个字符
# chunk_overlap: 文档块之间的重叠为100个字符
documents.create_node_group(name="sentences", transform=SentenceSplitter, chunk_size=1024, chunk_overlap=100)

# ===== 检索器参数配置示例 =====

# 1. 默认输出格式（DocNode对象列表）
# ppl = Retriever(documents, group_name="sentences", similarity="cosine", similarity_cut_off=0.003, topk=3)
# 返回: [DocNode1, DocNode2, DocNode3]

# 2. 字典格式输出
# ppl = Retriever(documents, group_name="sentences", similarity="cosine",
#                 similarity_cut_off=0.003, topk=3, output_format="dict")
# 返回: [{"content": "文本1", "metadata": {...}}, {"content": "文本2", "metadata": {...}}, ...]

# 3. 字典格式输出 + 连接
# ppl = Retriever(documents, group_name="sentences", similarity="cosine",
#                 similarity_cut_off=0.003, topk=3, output_format="dict", join=True)
# 返回: 将多个字典合并的结果

# 4. 内容格式输出（仅文本内容）
# ppl = Retriever(documents, group_name="sentences", similarity="cosine",
#                 similarity_cut_off=0.003, topk=3, output_format="content")
# 返回: ["文本1", "文本2", "文本3"]

# 5. 内容格式输出 + 默认连接
# ppl = Retriever(documents, group_name="sentences", similarity="cosine",
#                 similarity_cut_off=0.003, topk=3, output_format="content", join=True)
# 返回: "文本1文本2文本3"（无分隔符连接）

# 6. 内容格式输出 + 自定义分隔符连接
# 演示使用自定义分隔符连接多个检索结果
ppl = Retriever(documents,
                group_name="sentences",           # 使用sentences节点组
                similarity="cosine",              # 余弦相似度算法
                similarity_cut_off=0.003,        # 相似度阈值：过滤低相关性结果
                topk=3,                          # 返回前3个最相关结果
                output_format="content",         # 输出格式：仅返回文本内容
                join='11111111111111111111111111111')  # 自定义分隔符：用长串1连接

# ===== 执行检索测试 =====

# 执行检索查询
query = "何为天道"
nodes = ppl(query)

# 输出结果
print("=== 检索器参数配置演示 ===")
print(f"查询问题: {query}")
print(f"配置说明: 使用自定义分隔符连接的内容格式输出")
print(f"检索结果: {nodes}")
print("\n注意: 结果中的长串'1'就是我们设置的自定义分隔符")
