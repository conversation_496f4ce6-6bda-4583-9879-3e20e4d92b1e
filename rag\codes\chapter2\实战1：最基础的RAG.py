# 导入LazyLLM核心模块
from lazyllm import Document, Retriever
import lazyllm
# 导入Hugging Face数据集库
from datasets import load_dataset
# 导入操作系统模块
import os


# RAG 实战剖析 - 最基础的RAG系统实现

# 环境配置说明（可选）
# 将LazyLLM路径加入环境变量
# import sys
# sys.path.append("path/to/LazyLLM")
# # 设置商汤API密钥环境变量
# import os
# os.environ["LAZYLLM_SENSENOVA_API_KEY"] = ""
# os.environ["LAZYLLM_SENSENOVA_SECRET_KEY"] = ""

# 1.文档加载 📚
# RAG 文档读取 - 支持绝对路径和相对路径
# 传入绝对路径方式
doc = Document("/path/to/rag_master/")
print(f"实际传入路径为：{doc.manager._dataset_path}")

# 传入相对路径方式（需要配置环境变量）
# 需配置环境变量：export LAZYLLM_DATA_PATH="your_path"
# doc = Document("/paper/")

# 2.检索组件 🕵
# 创建文档对象，传入绝对路径
doc = Document("/path/to/rag_master/")

# 使用Retriever组件进行文档检索
# doc: 文档对象
# group_name: 节点组名称，使用内置的粗粒度切分策略"CoarseChunk"
# similarity: 相似度计算函数，使用BM25中文算法
# topk: 返回最相关的前3个文档片段
retriever = Retriever(doc, group_name=Document.CoarseChunk, similarity="bm25_chinese", topk=3)

# 调用检索器，传入查询问题
retriever_result = retriever("什么是道？")

# 打印检索结果，使用get_content()方法获取具体内容
print(retriever_result[0].get_content())

# 3.生成组件 🙋
# 配置大语言模型
api_key = ''  # API密钥（需要实际配置）
llm_prompt = "你是一只小猫，每次回答完问题都要加上喵喵喵"  # 自定义提示词
# 创建在线聊天模块，使用商汤SenseChat-5模型
llm = lazyllm.OnlineChatModule(source="sensenova", model="SenseChat-5").prompt(llm_prompt)
print(llm("早上好！"))
# >>> 早上好！喵喵喵


# RAG 知识库构建

# 1.数据集简介
'''
CMRC 2018（Chinese Machine Reading Comprehension 2018）数据集介绍：
- 这是一个中文阅读理解数据集
- 用于中文机器阅读理解的跨度提取任务
- 旨在增加该领域的语言多样性
- 数据集由人类专家在维基百科段落上注释的近20,000个真实问题组成
'''
dataset = load_dataset('cmrc2018')  # 加载CMRC2018数据集
# dataset = load_dataset('cmrc2018', cache_dir='path/to/datasets') # 可指定下载路径
print(dataset)

# 2.构建知识库
def create_KB(dataset):
    '''
    基于测试集中的context字段创建知识库
    功能：将数据集中的上下文文本按每10条为一组保存为txt文件
    '''
    Context = []
    # 提取所有上下文文本
    for i in dataset:
        Context.append(i['context'])
    Context = list(set(Context))  # 去重处理，获得256个唯一语料

    # 计算需要的文件数量
    chunk_size = 10  # 每个文件包含10条数据
    total_files = (len(Context) + chunk_size - 1) // chunk_size  # 向上取整计算文件数

    # 创建data_kb文件夹保存知识库语料
    os.makedirs("data_kb", exist_ok=True)

    # 按10条数据一组写入多个文件
    for i in range(total_files):
        chunk = Context[i * chunk_size: (i + 1) * chunk_size]  # 获取当前批次的10条数据
        file_name = f"./data_kb/part_{i+1}.txt"  # 生成文件名
        # 以UTF-8编码写入文件
        with open(file_name, "w", encoding="utf-8") as f:
            f.write("\n".join(chunk))  # 以换行符分隔写入文件

        # print(f"文件 {file_name} 写入完成！")  # 可选：提示当前文件已写入

# 调用函数创建知识库
create_KB(dataset['test'])
# 展示知识库中的部分内容
with open('data_kb/part_1.txt') as f:
    print(f.read())


# 实现最基础的 RAG 系统

# 1. 文档加载
# 创建文档对象，指定数据集路径
documents = lazyllm.Document(dataset_path="/path/to/rag_master/")

# 2. 检索组件定义
# 创建检索器，配置检索参数
retriever = lazyllm.Retriever(doc=documents, group_name="CoarseChunk", similarity="bm25_chinese", topk=3)

# 3. 生成组件定义
# 创建大语言模型实例
llm = lazyllm.OnlineChatModule(source="sensenova", model="SenseChat-5")

# 4. 提示词设计
# 定义AI助手的角色和任务说明
prompt = '''You will act as an AI question-answering assistant and complete a dialogue task.
In this task, you need to provide your answers based on the given context and questions.'''
# 配置聊天提示器，指定额外的上下文键
llm.prompt(lazyllm.ChatPrompter(instruction=prompt, extra_keys=['context_str']))

# 5. RAG推理过程
query = "什么是道？"  # 用户查询问题
# 使用检索器召回相关文档节点
doc_node_list = retriever(query=query)
# 将查询和召回的文档内容组合成字典，作为大模型的输入
res = llm({"query": query, "context_str": "".join([node.get_content() for node in doc_node_list])})

print(f'With RAG Answer: {res}')  # 输出基于RAG的回答

# 6. 对比实验：不使用RAG的回答
# 创建不使用RAG的大语言模型实例
llm_without_rag = lazyllm.OnlineChatModule(source="sensenova", model="SenseChat-5")
query = "什么是道？"
res = llm_without_rag(query)
print(f'Without RAG Answer: {res}')  # 输出不使用RAG的回答
