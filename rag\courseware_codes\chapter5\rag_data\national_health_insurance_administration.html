<!DOCTYPE html>
<html>
<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="renderer" content="webkit">
	<meta charset="utf-8" /><script language="javascript" src="/module/jslib/jquery/jquery.js"></script>
<script language="javascript" src="/module/jslib/urite/urite.min.js"></script>
<link href="/module/jslib/tag/css/infotag.css" type=text/css rel=stylesheet>
<meta name='WebId' content='1'>

	<title>国家医疗保障局 医保动态 严正声明</title>
<meta name='Maketime' content='2023-12-11 18:36:52'>
<meta name="SiteName" content="国家医疗保障局">
<meta name="SiteDomain" content="www.nhsa.gov.cn">
<meta name="SiteIDCode" content="bm83000001">
<meta name="ColumnName" content="医保动态">
<meta name="ColumnType" content="">
<meta name="ArticleTitle" content="严正声明">
<meta name="PubDate" content="2023-12-11 16:39">
<meta name="author" content="">
<meta name="description" content="">
<meta name="keywords" content="">
<meta name="contentSource" content="">
<meta name="url" content="http://www.nhsa.gov.cn/art/2023/12/11/art_52_11661.html">

<meta name="category" content="">
<meta name="language" content="中文">
<meta name="location" content="">
<meta name="department" content="">
<meta name="guid" content="20230011661">
<meta name="effectiveTime" content="0">
<meta name="level" content="0">
<meta name="serviceCategory" content="">
<meta name="serviceObject" content="">
<meta name="comments" content="">
<meta name="subject" content="">

	<!-- <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">  -->
	<link rel="stylesheet" type="text/css" href="/images/4/hanweb.css"/>
	<link rel="stylesheet" type="text/css" href="/images/4/layout.css"/>
	<link rel="stylesheet" type="text/css" href="/images/4/respon.css"/>
	<script src="/images/4/main.js" type="text/javascript" charset="utf-8"></script>
	<!--[if lt IE 9]>
    <script src="/images/4/html5shiv.js"></script>
    <script src="/images/4/respond.min.js"></script>
    <![endif]-->
	<style type="text/css">
	#zoom img{max-width:100%;}
	</style>
</head>
<body><div id='hiddenLocation' style='display:none;'><span id='ss_webid'>1</span><span id='col2_name'>国家医疗保障局政务服务窗口</span><span id='col2_url'>/col/col50/index.html</span><span id='col1_name'>医保动态</span><span id='col1_url'>/col/col52/index.html</span></div><div id='barrierfree_container'>
<div class="wrapper">

		<!--头部-->
		<script src="/script/0/1809201203401072.js"></script>
		
			<!-- 头部导航 -->
		<script src="/script/0/1809201514595701.js"></script>
		
	<!-- 文章正文 -->
	<div class="container">
		<div class="core clearfix column">
			<div class="mainbody">
			
				<div class="local clearfix">
					<table cellspacing="0" cellpadding="0" border="0">
    <tr>
        <td>
            <a href="/index.html" class="bt_link">首页</a>
        </td>
        <td>
            <table width="100%" cellspacing="0" cellpadding="0" border="0">
<tr>
<td>>
            
            
            <a href="/col/col50/index.html" class="bt_link">
                
                国家医疗保障局政务服务窗口
                
                
            </a>
            
            </td><td>>
            
            
            <a href="/col/col52/index.html" class="bt_link">
                
                医保动态
                
                
            </a>
            
            </td>
</tr></table>
        </td>
    </tr>
</table>
<style type="text/css">
    @media screen and (min-width: 0px) and (max-width: 850px) {
        #zoom img {
            max-width: 100%;
            height: auto;
        }
    }
</style>
				</div>
				<script type="text/javascript">
	function ChangeColor(ColorName) {
		document.getElementById("c").style.background = ColorName;
	}
</script>
<script language="javascript">
	function doZoom(size) {
		document.getElementById('zoom').style.fontSize = size + 'px';
	}
</script>

<div class="atricle" id="c">
	<div class="vision">
		视力保护色：
		<input onClick="ChangeColor('#F9F6AF')" type="button" name="bgcolor" style="background-color:#F9F6AF; width:15px;" align="absmiddle" />
		<input onClick="ChangeColor('#9DDBF4')" type="button" name="bgcolor22" style="background-color:#9DDBF4; width:15px;" align="absmiddle" />
		<input onClick="ChangeColor('#DEDEDE')" type="button" name="bgcolor2" style="background-color:#DEDEDE; width:15px;" align="absmiddle" />
		<input onClick="ChangeColor('#F9D1D9')" type="button" name="bgcolor2" style="background-color:#F9D1D9; width:15px;" align="absmiddle" />
		<input onClick="ChangeColor('#E4C8F8')" type="button" name="bgcolor2" style="background-color:#E4C8F8; width:15px;" align="absmiddle" />
		<input onClick="ChangeColor('#ffffff')" type="button" name="bgcolor2" style="background-color:#ffffff; width:15px;" align="absmiddle" />
	</div>
	<div class="atricle-title">
		<!--<$[标题名称(html)]>begin-->严正声明<!--<$[标题名称(html)]>end-->
	</div>
	<div class="sub">
		
		
		
		<span class="wzy-rq">日期：2023-12-11</span> <span class="wzy-fwcs">访问次数： <script language='javascript' src="/module/visitcount/articlehits.jsp?colid=52&artid=11661" >
 </script></span>
		<span class="wzy-zh">字号：[
						            <a href='javascript:doZoom(18)'>大</a>
						            <a href='javascript:doZoom(16)'>中</a>
						            <a href='javascript:doZoom(14)'>小</a> ]</span>
	</div>
	<div id="zoom">
        
    
		<!--<$[信息内容]>begin--><!--ZJEG_RSS.content.begin--><meta name="ContentStart"><p style="text-indent: 2em; text-align: justify;">近期发现有不法分子冒用中国医疗保险研究会（以下简称“研究会”）名义开展培训等活动，其活动函件的红头、公章、联系人及活动内容等各类信息均为伪造，此行为严重损害了中国医疗保险研究会的社会形象和声誉，侵害了相关人员的利益。在此，研究会严正声明:</p><p style="text-indent: 2em; text-align: justify;">1.严禁任何机构和个人冒用“中国医疗保险研究会”名称或简称举办各类活动。所有冒用研究会名义开展活动的机构和个人必须立即停止一切侵权行为。</p><p style="text-indent: 2em; text-align: justify;">2.研究会已向公安机关报案，并保留进一步诉诸法律依法追究侵权者法律责任的权利。</p><p style="text-indent: 2em; text-align: justify;">请各级医保部门和社会公众提高警惕，理性甄别，若对相关信息存疑，可致电核实，谨防上当受骗。</p><p style="text-indent: 2em; text-align: justify;">电话：010-50947536</p><p style="text-indent: 2em; text-align: justify;">特此声明。</p><p style="text-indent: 2em; text-align: right;">中国医疗保险研究会</p><p style="text-indent: 2em; text-align: right;">2023年12月11日</p><meta name="ContentEnd"><!--ZJEG_RSS.content.end--><!--<$[信息内容]>end-->

	</div>
	<div class="bottom clearfix">
		<div class="share fl clearfix">
			<!--<div class="fl">
						            </div>-->
			<DIV class="bshare-custom icon-medium fl">
				<span style="display: inline-block;float: left;margin-right: 15px;">分享到:</span>

				<div class="bdsharebuttonbox" style="display: inline-block;float: left;">
					<a href="#" class="bds_more" data-cmd="more"></a>
					<a href="#" class="bds_qzone" data-cmd="qzone" title="分享到QQ空间"></a>
					<a href="#" class="bds_tsina" data-cmd="tsina" title="分享到新浪微博"></a>
					<!--<a href="#" class="bds_tqq" data-cmd="tqq" title="分享到腾讯微博"></a>-->
					<a href="#" class="bds_renren" data-cmd="renren" title="分享到人人网"></a>
					<a href="#" class="bds_weixin" data-cmd="weixin" title="分享到微信"></a>
				</div>
				<script>
					window._bd_share_config = {
						"common": {
							"bdSnsKey": {},
							"bdText": "",
							"bdMini": "2",
							"bdMiniList": false,
							"bdPic": "",
							"bdStyle": "0",
							"bdSize": "24"
						},
						"share": {}
					};
					with(document) 0[(getElementsByTagName('head')[0] || body).appendChild(createElement('script')).src = 'http://bdimg.share.baidu.com/static/api/js/share.js?v=89860593.js?cdnversion=' + ~(-new Date() / 36e5)];
				</script>
			</DIV>

		</div>
		<div class="bottom-right fr">
			<a href="javascript:window.print()">【打印】</a>
			<a href="javascript:window.opener=null;window.open('','_self');window.close();">【关闭】</a>
		</div>
	</div>
</div>

<style>
@media screen and (min-width: 0px) and (max-width: 850px){
    video{
        width: 100%;
    }
}
</style>
<style>
    #mediaContent0_1{
        margin: 0 auto;
    }
    #mediaContent0_2{
        margin: 0 auto;
    }
    #mediaContent0_3{
        margin: 0 auto;
    }
</style>
		</div>

		</div>



	</div>

	<!--底部-->
	<script src="/script/0/2011171132063948.js"></script>
</div>
</div><!-- visitcount Begin --><iframe src='../../../../module/visitcount/visit.jsp?type=3&i_webid=1&i_columnid=52&i_articleid=11661' name="vishidden" id="vishidden" frameborder="0" style="width:0; height:0; display:none"></iframe><!-- visitcount End -->
<script language="javascript" src='/script/web_front.js'></script>
</body>
</html>