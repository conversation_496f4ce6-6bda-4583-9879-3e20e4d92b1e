乌石渔港是位于台湾宜兰县头城镇的观光渔港，渔港管理单位行政院农业委员会渔业署将其列为第一类渔港。乌石渔港自古就是台湾东北部出入的门户，现在和梗枋渔港及南方澳渔港并列宜兰3大赏鲸观光渔港。一年一度的头城抢孤也是在此地举办。乌石渔港，港口名称的由来，是由于港湾内有块巨大的黑礁石而得名。早期除了具有渔业功能外，也曾是宜兰最重要的对外贸易商港「乌石商港」，商业活动使得港务日益繁忙，全盛时期，东北角一带的民生物资都由此进出，当年来自江浙的「北船」、闽澳的「南船」、漳州、泉州、惠州的「唐山船」、鸡笼、艋舺的「彭仔船」等商船云集的盛况，也嬴得『石港春帆』的美称，被列为兰阳八景之一。在一次洪水带来大量泥沙堆积，让乌石商港失去功用而逐渐没落；直到1990年之后，重新整建成兼具观光事业的渔港，现在已是假日赏鲸、龟山岛往来的交通要津。乌石港扩建之后，因为突堤效应的结果，海沙不断累积在乌石港以北的外澳地区形成一大片沙滩，而乌石港以南头城海水浴场地区的沙量却逐年消失中。不断以消波块来阻挡海岸线向内侵蚀。乌石港的北堤因为海砂的不断累积，在外澳地区所形成一大片沙滩，地形使海浪易成为浪型，加上雪山隧道通车带来的交通便利，如今也成为假日的冲浪圣地。从原本只有一家蓝洋冲浪俱乐部，变成了10多家冲浪店。民俗活动方面，每到农历七月底乌石港区内会架起孤棚与饭栈以举办一年一度的头城抢孤活动，与恒春半岛的抢孤活动齐名。乌石礁为立于乌石港中之乌礁石，乌石港之名由此而来。地址：宜兰县头城镇港口里港口路15-7号电话：03-9782345
李忠臣（），原名董秦。幽州蓟县人。少年从军，以材力奋，为节度使薛楚玉部下。安史之乱时，随史思明攻打河阳，趁夜率五百人投降李光弼，唐肃宗召至京师，赐名李忠臣，赐良马、甲第。时立有战功，大败奚族首领阿布离。郭子仪军攻相州时，诸军皆溃，惟独李忠臣独胜。李忠臣对下贪暴凶淫，曾为辛京杲死罪开脱。任淮西节度使，与永平节度使李勉、河阳三城使马燧参与唐平李灵曜汴州之战，忠臣与马燧合军，进军汴州南，攻破李灵曜部。马燧知李忠臣暴戾，不入汴城。忠臣入城后，宋州刺史李僧惠与之争功，忠臣击杀之；忠臣又打算杀害刘昌，刘昌因此逃亡。李忠臣被任命为同平章事。大历十四年（779年）三月，李忠臣被李希烈驱逐，783年，拥立朱泚称帝，784年朱泚败后，被朝廷处死。兴元元年，并其子并诛斩之，时年六十九，籍没其家。
狭叶艾纳香（学名：）是菊科艾纳香属的植物。生长于海拔900米至1,900米的地区，多生长在山谷空旷湿润地，目前尚未由人工引种栽培。特征：粗壮、直立高大的亚灌木，株高可达2.5 m以上，茎中空，中部分枝。叶草质，线形，下部叶长25~35cm，叶浅锯齿缘，上表面被疏毛，下表面被毡毛。头花腋生及顶生，呈金字塔形的圆锥状排列。心花黄色。狭叶艾纳香是迟至1999年才发表的新种 菊科植物，初时被视为是特有种且数量稀少，属于特稀有植物，曾引起广泛讨论。本种仅见于 北部、东部低海拔山区及 兰屿，呈点状分布。中国大陆的云南、菲律宾、吕宋以北、巴丹群岛、台湾北部、台湾东部低海拔山区、台北坪林山区( 附近农户有拿来当野菜种植 )、兰屿。
牛车水（；）是新加坡的唐人街，是新加坡历史上重要的华人聚集地，为新加坡的著名旅游景点之一，位于新加坡欧南区（Outram）。1330年，中国元代民间商人、航海家汪大渊到访淡马锡，记录了此地已存在华人社区。这是新加坡最古老的唐人街之一，也是最大的一个。1819年，在英国人斯坦福·莱佛士登陆新加坡之前，中国南下的劳工已在这片区域从事槟榔与胡椒的种植。此后，从中国南来的华人越来越多，莱佛士索性把新加坡河西南部沿驳船码头（Boat Quay）一带的地区划为华人居住区。那一时期，新加坡还没有自来水设备，全岛所需要的水都得用牛车自安祥山（Ann Siang Hill）和史必灵街（Spring Street）的水井汲水载到此，于是这个以牛车载水供应用水的地区就称为牛车水。1822年，斯坦福·莱佛士制定了新加坡第一个城市规划，其中就将牛车水定为华人移民的居住区。此后随着新加坡的发展和人口的繁荣，牛车水变得过度拥挤，华人移民开始向新加坡的其他地区定居，1960年代组屋的出现才缓解了这一状况。1927年3月12日，由国民党左派带领的游行队伍在牛车水警局前与警方发生冲突，造成六死14伤，史称「牛车水事件」。注：除了牛车水外，新加坡其他地区的街道并没有官方中文译名，乃按当地约定俗成的译法或参考自各历史资料。位于宝塔街（俗称广合源街）及新桥路（New Bridge Road，俗称二马路）上的牛车水地铁站是前往当地的主要途径，此外还有多条巴士线路连接这一地区。
64DD（64 Disk Drive 在台又称作 N64磁气磁碟系统）为任天堂针对N64所推出的扩充磁碟机，可读取高达"64MB"高储存容量卡夹，任天堂欲用来兼顾高容量需求与读取速度的产品，也被认为是与Sony PlayStation的CD-ROM抗衡的产品。由于销售状况不佳，任天堂并未在日本境外发行，在市场上也是昙花一现而已的产品。64DD克服了N64容量不足的问题，也提供CD-ROM所没有的读写功能，且搭配了RANDNet的网路功能贩售，让N64也能具备网路连线的功能而后由于终止贩售的缘故，许多原先预计开发的64DD游戏也都中止开发，不过当时许多概念依旧保留在任天堂公司中，而将其经验应用在后来的NDS与Wii主机中。64DD首次登场是在1995年的任天堂 SpaceWorld 展览会展出，原先预定1996年推出，但一路延迟到1999年12月1日才正式在日本上市。销售方式不采取一般市面通路方式贩售，而改采会员邮购的方式，要购买的玩家必须先与任天堂签约成为会员，然后以12期分期付款的方式购买，每期2500日圆，之后每两个月会寄送一款64DD游戏。而由于延迟上市，市场已经被Sony的PlayStation攻占，且另一强敌Sega的新主机Dreamcast也准备上市，因此64DD从上市就充满著困难，而后由于销售状况不顺利，RANDNet服务于2000年年底宣布停止会员申请，并回收玩家手中的RANDNet硬体然后中止网路服务。许多原先预定推出在64DD上之游戏也因为64DD不断延迟上市而纷纷转移以卡带方式发行，其中最具代表性的游戏就是萨尔达传说 时光之笛，从发行到最后64DD总共推出8款游戏。为提供64DD之网路功能，任天堂与Recruit的合并公司Randnet负责提供64DD之网路连线功能，可经由家中电话线连线进行"线上连线对战、下载游戏试玩、听线上音乐与浏览网站"。后来RANDNet服务虽然仅仅营运11个月就宣告中止，但是此概念后来在NDS与Wii上获得广大成功。