# 导入LazyLLM主模块和相关组件
import lazyllm
from lazyllm import pipeline, parallel, bind, _0, _1

# 定义测试函数
def f1(input):
    """平方函数：计算输入的平方"""
    return input ** 2

def f21(input1, input2=0):
    """加法函数：两个输入相加再加1"""
    return input1 + input2 + 1

def f22(input1, input2=0):
    """减法函数：两个输入相加再减1"""
    return input1 + input2 - 1

def f3(in1='placeholder1', in2='placeholder2', in3='placeholder3'):
    """格式化输出函数：将三个输入格式化为字符串"""
    return f'get [input:{in1}], [f21:{in2}], [f23: {in3}]]'

# bind函数演示：数据绑定和参数传递
# bind允许将流水线中的不同阶段的输出绑定到函数的特定参数
with pipeline() as ppl1:
    # 第一步：计算输入的平方
    ppl1.f1 = f1

    # 第二步：并行执行两个函数
    with parallel() as ppl1.subprl2:
        ppl1.subprl2.path1 = f21  # 并行路径1：加法操作
        ppl1.subprl2.path2 = f22  # 并行路径2：减法操作

    # 第三步：使用bind绑定参数
    # ppl1.input: 绑定原始输入（2）
    # _0: 绑定并行结果的第一个元素（f21的结果）
    # _1: 绑定并行结果的第二个元素（f22的结果）
    ppl1.f3 = bind(f3, ppl1.input, _0, _1)

# 执行流水线：输入2
# 流程：2 -> f1(2)=4 -> parallel([f21(4)=5, f22(4)=3]) -> f3(2, 5, 3)
print("ppl1 out: ", ppl1(2))
