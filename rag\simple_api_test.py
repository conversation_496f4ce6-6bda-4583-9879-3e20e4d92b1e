#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的API连接测试脚本
不依赖lazyllm，直接测试API连接
"""

import requests
import json

def test_api_connection():
    """测试API连接"""
    print("=== API连接测试 ===")
    
    # 测试配置
    base_url = "https://ark.cn-beijing.volces.com/api/v3"
    api_key = "d210f6a1-7eff-4dd2-8778-ff3db4f8c54d"
    model = "ep-20250822223253-s98w6"
    
    print(f"测试URL: {base_url}")
    print(f"模型: {model}")
    print(f"API密钥: {api_key[:10]}...")
    
    # 1. 测试基础网络连接
    print("\n1. 测试基础网络连接...")
    try:
        response = requests.get(base_url, timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        if response.status_code == 200:
            print("   ✓ 网络连接正常")
        else:
            print(f"   ✗ 网络连接异常: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
    except requests.exceptions.Timeout:
        print("   ✗ 连接超时")
    except requests.exceptions.ConnectionError as e:
        print(f"   ✗ 连接错误: {e}")
    except Exception as e:
        print(f"   ✗ 其他错误: {e}")
    
    # 2. 测试API调用
    print("\n2. 测试API调用...")
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": "你好"}
            ],
            "max_tokens": 100
        }
        
        print(f"   请求URL: {base_url}/chat/completions")
        print(f"   请求头: {headers}")
        print(f"   请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("   ✓ API调用成功")
            print(f"   响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"   ✗ API调用失败")
            print(f"   错误响应: {response.text}")
            
    except Exception as e:
        print(f"   ✗ API调用异常: {e}")

def test_glm_embedding():
    """测试GLM嵌入API"""
    print("\n=== GLM嵌入模型API测试 ===")
    
    # GLM API配置
    api_key = "e37e81fae0e04aab9340b25a2f351e42.c34b4fosyCYIJKhb"
    base_url = "https://open.bigmodel.cn/api/paas/v4/embeddings"
    
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "embedding-2",
            "input": "测试文本"
        }
        
        print(f"   请求URL: {base_url}")
        print(f"   请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            base_url,
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("   ✓ GLM嵌入API调用成功")
            if 'data' in result and len(result['data']) > 0:
                embedding = result['data'][0]['embedding']
                print(f"   向量维度: {len(embedding)}")
            else:
                print(f"   响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"   ✗ GLM嵌入API调用失败")
            print(f"   错误响应: {response.text}")
            
    except Exception as e:
        print(f"   ✗ GLM嵌入API调用异常: {e}")

if __name__ == "__main__":
    test_api_connection()
    test_glm_embedding()
    
    print("\n=== 诊断建议 ===")
    print("如果测试失败，可能的原因和解决方案：")
    print("1. 网络连接问题 - 检查网络连接和防火墙设置")
    print("2. API密钥无效 - 验证API密钥是否正确且未过期")
    print("3. 模型名称错误 - 确认模型名称是否正确")
    print("4. 服务端问题 - 联系服务提供商确认服务状态")
    print("5. 请求格式问题 - 检查请求参数是否符合API规范")
