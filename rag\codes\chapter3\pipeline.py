# 导入LazyLLM主模块
import lazyllm

# 定义各种类型的函数和类，演示pipeline的灵活性

# 1. Lambda函数：将输入乘以2
f1 = lambda x: x * 2

# 2. 普通函数：将输入减1
def f2(input):
  return input - 1

# 3. 可调用类：实现加1操作
class AddOneFunctor(object):
  def __call__(self, x): return x + 1

# 4. 创建类实例
f3 = AddOneFunctor()

# 手动调用方式：逐步执行每个函数
inp = 2  # 输入值
x1 = f1(inp)  # 第一步：2 * 2 = 4
x2 = f2(x1)   # 第二步：4 - 1 = 3
x3 = f3(x2)   # 第三步：3 + 1 = 4
out_normal = AddOneFunctor()(x3)  # 第四步：4 + 1 = 5

# 使用LazyLLM pipeline数据流方式
# pipeline会自动按顺序执行所有函数，将前一个函数的输出作为下一个函数的输入
ppl = lazyllm.pipeline(f1, f2, f3, AddOneFunctor)
out_ppl1 = ppl(inp)  # 一次性执行整个流水线

# 输出结果对比
print(f"输入为{inp},手动调用输出:", out_normal)  # 应该输出5
print(f"输入为{inp},数据流输出:", out_ppl1)      # 应该输出5
